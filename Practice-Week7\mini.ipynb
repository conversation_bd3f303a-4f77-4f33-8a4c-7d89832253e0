{"cells": [{"cell_type": "code", "execution_count": null, "id": "dd35e0ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding sample recipes for demonstration...\n", "Sample recipes added! Ready to use the Recipe Book.\n", "\n", "Welcome to your Digital Recipe Book! 🍳\n", "\n", "==================================================\n", "🍳 DIGITAL RECIPE BOOK 🍳\n", "==================================================\n", "1. Add New Recipe\n", "2. View Recipe Details\n", "3. Search Recipes by Ingredient\n", "4. Update Recipe\n", "5. <PERSON><PERSON> Recipe\n", "6. List All Recipes\n", "7. Exit\n", "==================================================\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📝 ADD NEW RECIPE\n", "------------------------------\n"]}], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}