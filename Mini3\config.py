# Configuration file for Inventory Management System

import os
from typing import Dict, Any

# Database Configuration
DATABASE_CONFIG = {
    'mysql': {
        'host': 'localhost',
        'port': 3306,
        'database': 'inventory_db',
        'user': 'root',
        'password': 'password'
    },
    'postgresql': {
        'host': 'localhost',
        'port': 5432,
        'database': 'inventory_db',
        'user': 'postgres',
        'password': 'password'
    },
    'sqlite': {
        'database': 'inventory.db'
    }
}

# Current database type (change this to switch between databases)
CURRENT_DB = 'sqlite'  # Options: 'mysql', 'postgresql', 'sqlite'

# Application Configuration
APP_CONFIG = {
    'title': 'Inventory Management System',
    'version': '1.0.0',
    'window_size': '1200x800',
    'theme': 'default'
}

# User Roles
USER_ROLES = {
    'ADMIN': 'admin',
    'INVENTORY_MANAGER': 'inventory_manager',
    'SUPPLIER': 'supplier'
}

# Report Types
REPORT_TYPES = {
    'LOW_STOCK': 'low_stock',
    'SALES': 'sales',
    'INVENTORY': 'inventory',
    'USER_ACTIVITY': 'user_activity'
}

# Stock Alert Thresholds
STOCK_THRESHOLDS = {
    'LOW_STOCK': 10,
    'CRITICAL_STOCK': 5,
    'OUT_OF_STOCK': 0
}

# GUI Configuration
GUI_CONFIG = {
    'colors': {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'success': '#F18F01',
        'warning': '#C73E1D',
        'background': '#F5F5F5',
        'text': '#333333'
    },
    'fonts': {
        'default': ('Arial', 10),
        'heading': ('Arial', 14, 'bold'),
        'title': ('Arial', 16, 'bold')
    },
    'padding': {
        'small': 5,
        'medium': 10,
        'large': 20
    }
}

def get_database_config() -> Dict[str, Any]:
    """Get current database configuration"""
    return DATABASE_CONFIG.get(CURRENT_DB, DATABASE_CONFIG['sqlite'])

def get_connection_string() -> str:
    """Get database connection string based on current database type"""
    config = get_database_config()
    
    if CURRENT_DB == 'mysql':
        return f"mysql+pymysql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
    elif CURRENT_DB == 'postgresql':
        return f"postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
    else:  # sqlite
        return f"sqlite:///{config['database']}"
