# Login GUI for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox
from user_management import user_service
from config import GUI_CONFIG, APP_CONFIG

class LoginWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(f"{APP_CONFIG['title']} - Login")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Configure style
        self.setup_style()
        
        # Create login form
        self.create_login_form()
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.login())
        
        self.login_successful = False
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_style(self):
        """Setup GUI styling"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure colors
        colors = GUI_CONFIG['colors']
        style.configure('Title.TLabel', 
                       font=GUI_CONFIG['fonts']['title'],
                       foreground=colors['primary'])
        style.configure('Login.TButton',
                       font=GUI_CONFIG['fonts']['default'])
    
    def create_login_form(self):
        """Create the login form"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=GUI_CONFIG['padding']['large'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Inventory Management System", 
                               style='Title.TLabel')
        title_label.pack(pady=(0, GUI_CONFIG['padding']['large']))
        
        # Login form frame
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(expand=True)
        
        # Username field
        ttk.Label(form_frame, text="Username:", 
                 font=GUI_CONFIG['fonts']['default']).grid(row=0, column=0, 
                                                          sticky='e', 
                                                          padx=(0, GUI_CONFIG['padding']['small']),
                                                          pady=GUI_CONFIG['padding']['small'])
        
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(form_frame, textvariable=self.username_var, 
                                       font=GUI_CONFIG['fonts']['default'], width=20)
        self.username_entry.grid(row=0, column=1, 
                                pady=GUI_CONFIG['padding']['small'])
        self.username_entry.focus()
        
        # Password field
        ttk.Label(form_frame, text="Password:", 
                 font=GUI_CONFIG['fonts']['default']).grid(row=1, column=0, 
                                                          sticky='e',
                                                          padx=(0, GUI_CONFIG['padding']['small']),
                                                          pady=GUI_CONFIG['padding']['small'])
        
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(form_frame, textvariable=self.password_var, 
                                       show="*", font=GUI_CONFIG['fonts']['default'], 
                                       width=20)
        self.password_entry.grid(row=1, column=1, 
                                pady=GUI_CONFIG['padding']['small'])
        
        # Login button
        self.login_button = ttk.Button(form_frame, text="Login",
                                      command=self.login, style='Login.TButton')
        self.login_button.grid(row=2, column=0, columnspan=2,
                              pady=GUI_CONFIG['padding']['medium'])

        # Forgot password link
        forgot_password_button = ttk.Button(form_frame, text="Forgot Password?",
                                           command=self.forgot_password)
        forgot_password_button.grid(row=3, column=0, columnspan=2,
                                   pady=(0, GUI_CONFIG['padding']['small']))
        
        # Status label
        self.status_label = ttk.Label(form_frame, text="",
                                     font=GUI_CONFIG['fonts']['default'])
        self.status_label.grid(row=4, column=0, columnspan=2,
                              pady=GUI_CONFIG['padding']['small'])
        
        # Default credentials info
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(side=tk.BOTTOM, pady=GUI_CONFIG['padding']['medium'])
        
        info_text = "Default Login:\nUsername: admin\nPassword: admin123"
        ttk.Label(info_frame, text=info_text, 
                 font=('Arial', 8), 
                 foreground='gray',
                 justify=tk.CENTER).pack()
    
    def login(self):
        """Handle login attempt"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            self.show_status("Please enter both username and password", "error")
            return
        
        # Disable login button during authentication
        self.login_button.config(state='disabled')
        self.show_status("Authenticating...", "info")
        
        # Update GUI
        self.root.update()
        
        try:
            if user_service.authenticate(username, password):
                self.show_status("Login successful!", "success")
                self.login_successful = True
                self.root.after(1000, self.root.destroy)  # Close after 1 second
            else:
                self.show_status("Invalid username or password", "error")
                self.password_var.set("")  # Clear password field
                self.password_entry.focus()
        except Exception as e:
            self.show_status(f"Login error: {str(e)}", "error")
        finally:
            self.login_button.config(state='normal')
    
    def show_status(self, message: str, status_type: str = "info"):
        """Show status message with appropriate color"""
        colors = GUI_CONFIG['colors']
        color_map = {
            'info': colors['text'],
            'success': colors['success'],
            'error': colors['warning']
        }
        
        self.status_label.config(text=message,
                                foreground=color_map.get(status_type, colors['text']))

    def forgot_password(self):
        """Handle forgot password"""
        from dialogs import ForgotPasswordDialog
        dialog = ForgotPasswordDialog(self.root)
        if dialog.result:
            messagebox.showinfo("Password Reset",
                               f"A temporary password has been generated.\n\nNew password: {dialog.result['temp_password']}\n\nPlease use this password to login and change it immediately.")
    
    def run(self):
        """Run the login window"""
        self.root.mainloop()
        return self.login_successful

def show_login():
    """Show login window and return success status"""
    login_window = LoginWindow()
    return login_window.run()

if __name__ == "__main__":
    # For testing
    from database import db
    
    # Initialize database
    if db.connect():
        db.create_tables()
        
        # Show login
        if show_login():
            print(f"Login successful! Current user: {user_service.current_user.username}")
        else:
            print("Login failed or cancelled")
        
        db.disconnect()
    else:
        print("Failed to connect to database")
