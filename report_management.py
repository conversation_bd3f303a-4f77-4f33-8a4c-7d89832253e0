# Report Management Service for Inventory Management System

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from models import Product, Transaction, User, Category
from database import db
from user_management import user_service
from product_management import product_service
from config import REPORT_TYPES, STOCK_THRESHOLDS
import json

class ReportManagementService:
    def __init__(self):
        pass
    
    def generate_low_stock_report(self, threshold: int = None) -> Dict[str, Any]:
        """Generate low stock alert report"""
        if threshold is None:
            threshold = STOCK_THRESHOLDS['LOW_STOCK']
        
        low_stock_products = product_service.get_low_stock_products(threshold)
        
        report_data = {
            'report_type': REPORT_TYPES['LOW_STOCK'],
            'generated_at': datetime.now().isoformat(),
            'threshold': threshold,
            'total_products': len(low_stock_products),
            'products': []
        }
        
        for product in low_stock_products:
            category = Category.get_by_id(product.category_id) if product.category_id else None
            supplier = User.get_by_id(product.supplier_id) if product.supplier_id else None
            
            product_data = {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'current_quantity': product.quantity,
                'min_stock_level': product.min_stock_level,
                'category': category.name if category else 'Uncategorized',
                'supplier': supplier.full_name if supplier else 'No Supplier',
                'price': product.price,
                'status': self._get_stock_status(product.quantity, product.min_stock_level)
            }
            report_data['products'].append(product_data)
        
        # Save report to database
        self._save_report(REPORT_TYPES['LOW_STOCK'], report_data)
        
        return report_data
    
    def generate_inventory_report(self, category_id: int = None) -> Dict[str, Any]:
        """Generate comprehensive inventory report"""
        if category_id:
            products = product_service.get_products_by_category(category_id)
            category = Category.get_by_id(category_id)
            report_title = f"Inventory Report - {category.name if category else 'Unknown Category'}"
        else:
            products = product_service.get_all_products()
            report_title = "Complete Inventory Report"
        
        report_data = {
            'report_type': REPORT_TYPES['INVENTORY'],
            'generated_at': datetime.now().isoformat(),
            'title': report_title,
            'category_id': category_id,
            'total_products': len(products),
            'summary': {
                'total_items': 0,
                'total_value': 0,
                'low_stock_items': 0,
                'out_of_stock_items': 0
            },
            'products': []
        }
        
        for product in products:
            category = Category.get_by_id(product.category_id) if product.category_id else None
            supplier = User.get_by_id(product.supplier_id) if product.supplier_id else None
            
            product_value = product.quantity * product.price
            stock_status = self._get_stock_status(product.quantity, product.min_stock_level)
            
            product_data = {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'description': product.description,
                'category': category.name if category else 'Uncategorized',
                'supplier': supplier.full_name if supplier else 'No Supplier',
                'quantity': product.quantity,
                'price': product.price,
                'total_value': product_value,
                'min_stock_level': product.min_stock_level,
                'status': stock_status
            }
            
            report_data['products'].append(product_data)
            
            # Update summary
            report_data['summary']['total_items'] += product.quantity
            report_data['summary']['total_value'] += product_value
            
            if stock_status == 'Low Stock':
                report_data['summary']['low_stock_items'] += 1
            elif stock_status == 'Out of Stock':
                report_data['summary']['out_of_stock_items'] += 1
        
        # Save report to database
        self._save_report(REPORT_TYPES['INVENTORY'], report_data)
        
        return report_data
    
    def generate_sales_report(self, start_date: datetime = None, 
                            end_date: datetime = None) -> Dict[str, Any]:
        """Generate sales report based on outgoing transactions"""
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)  # Last 30 days
        if not end_date:
            end_date = datetime.now()
        
        # Get outgoing transactions (sales)
        query = """
            SELECT t.*, p.name as product_name, p.sku, p.price as current_price,
                   u.full_name as user_name, c.name as category_name
            FROM transactions t
            JOIN products p ON t.product_id = p.id
            JOIN users u ON t.user_id = u.id
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE t.transaction_type = 'OUT' 
            AND t.created_at BETWEEN ? AND ?
            ORDER BY t.created_at DESC
        """
        
        transactions = db.execute_query(query, (start_date.isoformat(), end_date.isoformat()))
        
        report_data = {
            'report_type': REPORT_TYPES['SALES'],
            'generated_at': datetime.now().isoformat(),
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'summary': {
                'total_transactions': len(transactions) if transactions else 0,
                'total_quantity_sold': 0,
                'total_revenue': 0,
                'average_transaction_value': 0
            },
            'transactions': [],
            'product_summary': {},
            'category_summary': {}
        }
        
        if transactions:
            for transaction in transactions:
                transaction_value = (transaction['price'] or transaction['current_price']) * transaction['quantity']
                
                transaction_data = {
                    'id': transaction['id'],
                    'product_name': transaction['product_name'],
                    'sku': transaction['sku'],
                    'category': transaction['category_name'] or 'Uncategorized',
                    'quantity': transaction['quantity'],
                    'unit_price': transaction['price'] or transaction['current_price'],
                    'total_value': transaction_value,
                    'user': transaction['user_name'],
                    'date': transaction['created_at'],
                    'notes': transaction['notes']
                }
                
                report_data['transactions'].append(transaction_data)
                
                # Update summary
                report_data['summary']['total_quantity_sold'] += transaction['quantity']
                report_data['summary']['total_revenue'] += transaction_value
                
                # Update product summary
                product_key = f"{transaction['product_name']} ({transaction['sku']})"
                if product_key not in report_data['product_summary']:
                    report_data['product_summary'][product_key] = {
                        'quantity_sold': 0,
                        'revenue': 0
                    }
                report_data['product_summary'][product_key]['quantity_sold'] += transaction['quantity']
                report_data['product_summary'][product_key]['revenue'] += transaction_value
                
                # Update category summary
                category = transaction['category_name'] or 'Uncategorized'
                if category not in report_data['category_summary']:
                    report_data['category_summary'][category] = {
                        'quantity_sold': 0,
                        'revenue': 0
                    }
                report_data['category_summary'][category]['quantity_sold'] += transaction['quantity']
                report_data['category_summary'][category]['revenue'] += transaction_value
            
            # Calculate average transaction value
            if report_data['summary']['total_transactions'] > 0:
                report_data['summary']['average_transaction_value'] = (
                    report_data['summary']['total_revenue'] / 
                    report_data['summary']['total_transactions']
                )
        
        # Save report to database
        self._save_report(REPORT_TYPES['SALES'], report_data)
        
        return report_data
    
    def generate_user_activity_report(self, user_id: int = None, 
                                    start_date: datetime = None,
                                    end_date: datetime = None) -> Dict[str, Any]:
        """Generate user activity report"""
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)
        if not end_date:
            end_date = datetime.now()
        
        query = """
            SELECT t.*, p.name as product_name, p.sku, u.full_name as user_name, u.role
            FROM transactions t
            JOIN products p ON t.product_id = p.id
            JOIN users u ON t.user_id = u.id
            WHERE t.created_at BETWEEN ? AND ?
        """
        params = [start_date.isoformat(), end_date.isoformat()]
        
        if user_id:
            query += " AND t.user_id = ?"
            params.append(user_id)
        
        query += " ORDER BY t.created_at DESC"
        
        transactions = db.execute_query(query, tuple(params))
        
        report_data = {
            'report_type': REPORT_TYPES['USER_ACTIVITY'],
            'generated_at': datetime.now().isoformat(),
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'user_id': user_id,
            'summary': {
                'total_transactions': len(transactions) if transactions else 0,
                'users_active': 0
            },
            'transactions': [],
            'user_summary': {}
        }
        
        if transactions:
            active_users = set()
            
            for transaction in transactions:
                transaction_data = {
                    'id': transaction['id'],
                    'user_name': transaction['user_name'],
                    'user_role': transaction['role'],
                    'product_name': transaction['product_name'],
                    'sku': transaction['sku'],
                    'transaction_type': transaction['transaction_type'],
                    'quantity': transaction['quantity'],
                    'price': transaction['price'],
                    'date': transaction['created_at'],
                    'notes': transaction['notes']
                }
                
                report_data['transactions'].append(transaction_data)
                active_users.add(transaction['user_id'])
                
                # Update user summary
                user_key = transaction['user_name']
                if user_key not in report_data['user_summary']:
                    report_data['user_summary'][user_key] = {
                        'role': transaction['role'],
                        'transaction_count': 0,
                        'stock_in': 0,
                        'stock_out': 0
                    }
                
                report_data['user_summary'][user_key]['transaction_count'] += 1
                if transaction['transaction_type'] == 'IN':
                    report_data['user_summary'][user_key]['stock_in'] += transaction['quantity']
                elif transaction['transaction_type'] == 'OUT':
                    report_data['user_summary'][user_key]['stock_out'] += transaction['quantity']
            
            report_data['summary']['users_active'] = len(active_users)
        
        # Save report to database
        self._save_report(REPORT_TYPES['USER_ACTIVITY'], report_data)
        
        return report_data
    
    def get_recent_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent reports"""
        result = db.execute_query(
            """SELECT r.*, u.full_name as generated_by_name 
               FROM reports r 
               JOIN users u ON r.generated_by = u.id 
               ORDER BY r.created_at DESC LIMIT ?""",
            (limit,)
        )
        
        reports = []
        if result:
            for row in result:
                report = {
                    'id': row['id'],
                    'report_type': row['report_type'],
                    'generated_by': row['generated_by_name'],
                    'created_at': row['created_at']
                }
                
                # Parse report data if needed
                if row['report_data']:
                    try:
                        report['data'] = json.loads(row['report_data'])
                    except:
                        report['data'] = None
                
                reports.append(report)
        
        return reports
    
    def _get_stock_status(self, quantity: int, min_stock_level: int) -> str:
        """Get stock status based on quantity and minimum level"""
        if quantity <= STOCK_THRESHOLDS['OUT_OF_STOCK']:
            return 'Out of Stock'
        elif quantity <= STOCK_THRESHOLDS['CRITICAL_STOCK']:
            return 'Critical Stock'
        elif quantity <= min_stock_level:
            return 'Low Stock'
        else:
            return 'In Stock'
    
    def _save_report(self, report_type: str, report_data: Dict[str, Any]) -> bool:
        """Save report to database"""
        if not user_service.current_user:
            return False
        
        query = """INSERT INTO reports (report_type, generated_by, report_data) 
                   VALUES (?, ?, ?)"""
        params = (report_type, user_service.current_user.id, json.dumps(report_data))
        
        return db.execute_update(query, params)

# Global report management service instance
report_service = ReportManagementService()
