lst_of_beverages = {
                    "Usa": {"coffee": 0, "tea": 0},
                    "Bott": {"coffee": 0, "tea": 0},
                    "Marya": {"coffee": 0, "tea": 0},
                    }
0
while True:
    name = input("Name: ").capitalize()
    if name == "":
        break
    if name not in lst_of_beverages:
        antwort  = input("Shall we include "+ name +
        " in the list? (yes/no): ").lower()
        if antwort.lower() in ["yes", "y"]:
            lst_of_beverages[name] = {"coffee": 0, "tea": 0}
        else:
            print(name +" Name don't have in the list.")
            continue
    drink = input("Enter drink (coffee/tea): ").lower()
    lst_of_beverages[name][drink] += 1
print(lst_of_beverages)


