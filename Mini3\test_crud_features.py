# Test script for CRUD features and password reset functionality

from database import db
from user_management import user_service
from product_management import product_service
from report_management import report_service
from models import User, Product, Category

def test_user_crud():
    """Test user CRUD operations"""
    print("\n" + "="*50)
    print("TESTING USER CRUD OPERATIONS")
    print("="*50)
    
    # Authenticate as admin
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Admin authentication failed")
        return False
    
    print("✅ Admin authenticated")
    
    # Test Create User
    try:
        success = user_service.create_user(
            username='testmanager',
            password='manager123',
            email='<EMAIL>',
            role='inventory_manager',
            full_name='Test Manager'
        )
        
        if success:
            print("✅ User creation successful")
        else:
            print("❌ User creation failed")
            return False
    except Exception as e:
        print(f"❌ User creation error: {e}")
        return False
    
    # Test Read User
    user = User.get_by_username('testmanager')
    if user:
        print(f"✅ User retrieved: {user.full_name}")
    else:
        print("❌ User retrieval failed")
        return False
    
    # Test Update User
    try:
        success = user_service.update_user(
            user_id=user.id,
            full_name='Updated Test Manager',
            email='<EMAIL>'
        )
        
        if success:
            print("✅ User update successful")
        else:
            print("❌ User update failed")
            return False
    except Exception as e:
        print(f"❌ User update error: {e}")
        return False
    
    # Test Password Change
    try:
        success = user_service.change_password(user.id, 'newpassword123')
        if success:
            print("✅ Password change successful")
        else:
            print("❌ Password change failed")
            return False
    except Exception as e:
        print(f"❌ Password change error: {e}")
        return False
    
    # Test User Deactivation
    try:
        success = user_service.deactivate_user(user.id)
        if success:
            print("✅ User deactivation successful")
        else:
            print("❌ User deactivation failed")
            return False
    except Exception as e:
        print(f"❌ User deactivation error: {e}")
        return False
    
    # Test User Reactivation
    try:
        success = user_service.activate_user(user.id)
        if success:
            print("✅ User reactivation successful")
        else:
            print("❌ User reactivation failed")
            return False
    except Exception as e:
        print(f"❌ User reactivation error: {e}")
        return False
    
    return True

def test_product_crud():
    """Test product CRUD operations"""
    print("\n" + "="*50)
    print("TESTING PRODUCT CRUD OPERATIONS")
    print("="*50)
    
    # Test Create Category
    try:
        success = product_service.create_category(
            name='Electronics',
            description='Electronic devices and components'
        )
        
        if success:
            print("✅ Category creation successful")
        else:
            print("❌ Category creation failed")
            return False
    except Exception as e:
        print(f"❌ Category creation error: {e}")
        return False
    
    # Get category for product creation
    categories = product_service.get_all_categories()
    if not categories:
        print("❌ No categories found")
        return False
    
    category_id = categories[0].id
    print(f"✅ Category retrieved: {categories[0].name}")
    
    # Test Create Product
    try:
        success = product_service.create_product(
            name='Test Laptop',
            description='A test laptop for CRUD testing',
            category_id=category_id,
            sku='LAPTOP001',
            price=999.99,
            quantity=50,
            min_stock_level=5
        )
        
        if success:
            print("✅ Product creation successful")
        else:
            print("❌ Product creation failed")
            return False
    except Exception as e:
        print(f"❌ Product creation error: {e}")
        return False
    
    # Test Read Product
    products = product_service.get_all_products()
    if not products:
        print("❌ No products found")
        return False
    
    test_product = None
    for product in products:
        if product.sku == 'LAPTOP001':
            test_product = product
            break
    
    if test_product:
        print(f"✅ Product retrieved: {test_product.name}")
    else:
        print("❌ Product retrieval failed")
        return False
    
    # Test Update Product
    try:
        success = product_service.update_product(
            product_id=test_product.id,
            name='Updated Test Laptop',
            price=1199.99,
            min_stock_level=10
        )
        
        if success:
            print("✅ Product update successful")
        else:
            print("❌ Product update failed")
            return False
    except Exception as e:
        print(f"❌ Product update error: {e}")
        return False
    
    # Test Stock Operations
    try:
        # Stock in
        success = product_service.stock_in(test_product.id, 25, 999.99, 'Test stock in')
        if success:
            print("✅ Stock in operation successful")
        else:
            print("❌ Stock in operation failed")
            return False
        
        # Stock out
        success = product_service.stock_out(test_product.id, 10, 1199.99, 'Test stock out')
        if success:
            print("✅ Stock out operation successful")
        else:
            print("❌ Stock out operation failed")
            return False
    except Exception as e:
        print(f"❌ Stock operation error: {e}")
        return False
    
    # Test Search Products
    search_results = product_service.search_products('Laptop')
    if search_results:
        print(f"✅ Product search successful: {len(search_results)} results")
    else:
        print("❌ Product search failed")
        return False
    
    return True

def test_password_reset():
    """Test password reset functionality"""
    print("\n" + "="*50)
    print("TESTING PASSWORD RESET FUNCTIONALITY")
    print("="*50)
    
    # Test forgot password reset
    try:
        # Use the updated email from the user update test
        temp_password = user_service.forgot_password_reset('<EMAIL>')
        if temp_password:
            print(f"✅ Forgot password reset successful: {temp_password}")
        else:
            print("❌ Forgot password reset failed - user not found")
            return False
    except Exception as e:
        print(f"❌ Forgot password reset error: {e}")
        return False
    
    # Test password strength validation
    weak_password = "123"
    strong_password = "StrongPass123"
    
    is_strong, message = user_service.validate_password_strength(weak_password)
    if not is_strong:
        print(f"✅ Weak password correctly rejected: {message}")
    else:
        print("❌ Weak password incorrectly accepted")
        return False
    
    is_strong, message = user_service.validate_password_strength(strong_password)
    if is_strong:
        print(f"✅ Strong password correctly accepted: {message}")
    else:
        print(f"❌ Strong password incorrectly rejected: {message}")
        return False
    
    return True

def test_advanced_reports():
    """Test advanced reporting features"""
    print("\n" + "="*50)
    print("TESTING ADVANCED REPORTING")
    print("="*50)
    
    try:
        # Test low stock report with custom threshold
        low_stock_report = report_service.generate_low_stock_report(20)
        print(f"✅ Low stock report generated: {low_stock_report['total_products']} products")
        
        # Test inventory report with category filter
        categories = product_service.get_all_categories()
        if categories:
            inventory_report = report_service.generate_inventory_report(categories[0].id)
            print(f"✅ Category inventory report generated: {inventory_report['total_products']} products")
        
        # Test sales report
        sales_report = report_service.generate_sales_report()
        print(f"✅ Sales report generated: {sales_report['summary']['total_transactions']} transactions")
        
        # Test user activity report
        activity_report = report_service.generate_user_activity_report()
        print(f"✅ User activity report generated: {activity_report['summary']['total_transactions']} transactions")
        
        return True
    except Exception as e:
        print(f"❌ Advanced reporting error: {e}")
        return False

def test_data_integrity():
    """Test data integrity and constraints"""
    print("\n" + "="*50)
    print("TESTING DATA INTEGRITY")
    print("="*50)
    
    # Test duplicate username prevention
    try:
        success = user_service.create_user(
            username='admin',  # Duplicate username
            password='test123',
            email='<EMAIL>',
            role='supplier',
            full_name='Duplicate User'
        )
        
        if not success:
            print("✅ Duplicate username correctly prevented")
        else:
            print("❌ Duplicate username incorrectly allowed")
            return False
    except ValueError as e:
        print(f"✅ Duplicate username correctly prevented: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    # Test duplicate SKU prevention
    try:
        categories = product_service.get_all_categories()
        success = product_service.create_product(
            name='Duplicate SKU Product',
            description='Test duplicate SKU',
            category_id=categories[0].id if categories else 1,
            sku='LAPTOP001',  # Duplicate SKU
            price=100.00,
            quantity=10
        )
        
        if not success:
            print("✅ Duplicate SKU correctly prevented")
        else:
            print("❌ Duplicate SKU incorrectly allowed")
            return False
    except ValueError as e:
        print(f"✅ Duplicate SKU correctly prevented: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def run_all_crud_tests():
    """Run all CRUD and advanced feature tests"""
    print("=" * 70)
    print("INVENTORY MANAGEMENT SYSTEM - CRUD & ADVANCED FEATURES TEST")
    print("=" * 70)
    
    # Initialize database
    if not db.connect():
        print("❌ Database connection failed")
        return False
    
    if not db.create_tables():
        print("❌ Table creation failed")
        return False
    
    tests = [
        ("User CRUD Operations", test_user_crud),
        ("Product CRUD Operations", test_product_crud),
        ("Password Reset Functionality", test_password_reset),
        ("Advanced Reporting", test_advanced_reports),
        ("Data Integrity", test_data_integrity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_function in tests:
        try:
            print(f"\n🔄 Running {test_name}...")
            if test_function():
                print(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - CRASHED: {e}")
    
    print("\n" + "=" * 70)
    print(f"CRUD & ADVANCED FEATURES TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 70)
    
    if passed == total:
        print("🎉 All CRUD and advanced features are working correctly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    # Clean up
    if db.connection:
        db.disconnect()
    
    return passed == total

if __name__ == "__main__":
    run_all_crud_tests()
