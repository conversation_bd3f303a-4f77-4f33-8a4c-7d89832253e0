# Main GUI Application for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox
from user_management import user_service
from config import GUI_CONFIG, APP_CONFIG, USER_ROLES
from dashboard_gui import DashboardFrame
import sys

class InventoryManagementApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(APP_CONFIG['title'])
        self.root.geometry(APP_CONFIG['window_size'])
        self.root.minsize(800, 600)
        
        # Configure style
        self.setup_style()
        
        # Create main interface
        self.create_main_interface()
        
        # Current active frame
        self.current_frame = None
        
        # Show dashboard by default
        self.show_dashboard()
    
    def setup_style(self):
        """Setup application styling"""
        style = ttk.Style()
        style.theme_use('clam')
        
        colors = GUI_CONFIG['colors']
        
        # Configure notebook style
        style.configure('TNotebook', background=colors['background'])
        style.configure('TNotebook.Tab', padding=[12, 8])
        
        # Configure button styles
        style.configure('Menu.TButton', font=GUI_CONFIG['fonts']['default'])
        style.configure('Action.TButton', font=GUI_CONFIG['fonts']['default'])
    
    def create_main_interface(self):
        """Create the main application interface"""
        # Create main menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content area
        self.create_content_area()
        
        # Create status bar
        self.create_status_bar()
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Logout", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.exit_application)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Dashboard", command=self.show_dashboard)
        view_menu.add_command(label="Products", command=self.show_products)
        view_menu.add_command(label="Reports", command=self.show_reports)
        
        # Admin menu (only for admin users)
        current_user = user_service.get_current_user()
        if current_user and current_user.role == USER_ROLES['ADMIN']:
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="Admin", menu=admin_menu)
            admin_menu.add_command(label="User Management", command=self.show_user_management)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create application toolbar"""
        toolbar_frame = ttk.Frame(self.root)
        toolbar_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['small'], 
                          pady=GUI_CONFIG['padding']['small'])
        
        # Navigation buttons
        ttk.Button(toolbar_frame, text="Dashboard", 
                  command=self.show_dashboard, 
                  style='Menu.TButton').pack(side=tk.LEFT, 
                                           padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(toolbar_frame, text="Products", 
                  command=self.show_products, 
                  style='Menu.TButton').pack(side=tk.LEFT, 
                                           padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(toolbar_frame, text="Reports", 
                  command=self.show_reports, 
                  style='Menu.TButton').pack(side=tk.LEFT, 
                                           padx=GUI_CONFIG['padding']['small'])
        
        # Admin button (only for admin users)
        current_user = user_service.get_current_user()
        if current_user and current_user.role == USER_ROLES['ADMIN']:
            ttk.Button(toolbar_frame, text="Users", 
                      command=self.show_user_management, 
                      style='Menu.TButton').pack(side=tk.LEFT, 
                                               padx=GUI_CONFIG['padding']['small'])
        
        # Right side buttons
        ttk.Button(toolbar_frame, text="Logout", 
                  command=self.logout, 
                  style='Menu.TButton').pack(side=tk.RIGHT, 
                                           padx=GUI_CONFIG['padding']['small'])
        
        # User info
        if current_user:
            user_info = f"Logged in as: {current_user.full_name} ({current_user.role.replace('_', ' ').title()})"
            ttk.Label(toolbar_frame, text=user_info, 
                     font=GUI_CONFIG['fonts']['default']).pack(side=tk.RIGHT, 
                                                              padx=GUI_CONFIG['padding']['medium'])
    
    def create_content_area(self):
        """Create main content area"""
        self.content_frame = ttk.Frame(self.root)
        self.content_frame.pack(fill=tk.BOTH, expand=True, 
                               padx=GUI_CONFIG['padding']['small'], 
                               pady=GUI_CONFIG['padding']['small'])
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        
        ttk.Label(self.status_frame, textvariable=self.status_var, 
                 relief=tk.SUNKEN, anchor=tk.W).pack(fill=tk.X, 
                                                    padx=GUI_CONFIG['padding']['small'], 
                                                    pady=GUI_CONFIG['padding']['small'])
    
    def clear_content(self):
        """Clear current content"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        self.current_frame = None
    
    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content()
        self.status_var.set("Dashboard")
        
        try:
            dashboard = DashboardFrame(self.content_frame)
            dashboard.get_frame().pack(fill=tk.BOTH, expand=True)
            self.current_frame = dashboard
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load dashboard: {str(e)}")
    
    def show_products(self):
        """Show products management"""
        self.clear_content()
        self.status_var.set("Product Management")
        
        # Create placeholder for products interface
        placeholder_frame = ttk.Frame(self.content_frame)
        placeholder_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(placeholder_frame, text="Product Management", 
                 font=GUI_CONFIG['fonts']['title']).pack(pady=GUI_CONFIG['padding']['large'])
        
        ttk.Label(placeholder_frame, text="Product management interface will be implemented here.", 
                 font=GUI_CONFIG['fonts']['default']).pack()
        
        # Add some basic functionality buttons
        button_frame = ttk.Frame(placeholder_frame)
        button_frame.pack(pady=GUI_CONFIG['padding']['medium'])
        
        ttk.Button(button_frame, text="Add Product", 
                  command=self.add_product_placeholder).pack(side=tk.LEFT, 
                                                           padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(button_frame, text="View Products", 
                  command=self.view_products_placeholder).pack(side=tk.LEFT, 
                                                             padx=GUI_CONFIG['padding']['small'])
    
    def show_reports(self):
        """Show reports"""
        self.clear_content()
        self.status_var.set("Reports")
        
        # Create placeholder for reports interface
        placeholder_frame = ttk.Frame(self.content_frame)
        placeholder_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(placeholder_frame, text="Reports", 
                 font=GUI_CONFIG['fonts']['title']).pack(pady=GUI_CONFIG['padding']['large'])
        
        ttk.Label(placeholder_frame, text="Reports interface will be implemented here.", 
                 font=GUI_CONFIG['fonts']['default']).pack()
        
        # Add some basic functionality buttons
        button_frame = ttk.Frame(placeholder_frame)
        button_frame.pack(pady=GUI_CONFIG['padding']['medium'])
        
        ttk.Button(button_frame, text="Low Stock Report", 
                  command=self.low_stock_report_placeholder).pack(side=tk.LEFT, 
                                                                 padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(button_frame, text="Inventory Report", 
                  command=self.inventory_report_placeholder).pack(side=tk.LEFT, 
                                                                 padx=GUI_CONFIG['padding']['small'])
    
    def show_user_management(self):
        """Show user management (admin only)"""
        current_user = user_service.get_current_user()
        if not current_user or current_user.role != USER_ROLES['ADMIN']:
            messagebox.showerror("Access Denied", "You don't have permission to access user management.")
            return
        
        self.clear_content()
        self.status_var.set("User Management")
        
        # Create placeholder for user management interface
        placeholder_frame = ttk.Frame(self.content_frame)
        placeholder_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(placeholder_frame, text="User Management", 
                 font=GUI_CONFIG['fonts']['title']).pack(pady=GUI_CONFIG['padding']['large'])
        
        ttk.Label(placeholder_frame, text="User management interface will be implemented here.", 
                 font=GUI_CONFIG['fonts']['default']).pack()
    
    def add_product_placeholder(self):
        """Placeholder for add product functionality"""
        messagebox.showinfo("Feature", "Add Product feature will be implemented in the next phase.")
    
    def view_products_placeholder(self):
        """Placeholder for view products functionality"""
        messagebox.showinfo("Feature", "View Products feature will be implemented in the next phase.")
    
    def low_stock_report_placeholder(self):
        """Placeholder for low stock report"""
        messagebox.showinfo("Feature", "Low Stock Report feature will be implemented in the next phase.")
    
    def inventory_report_placeholder(self):
        """Placeholder for inventory report"""
        messagebox.showinfo("Feature", "Inventory Report feature will be implemented in the next phase.")
    
    def logout(self):
        """Logout current user"""
        if messagebox.askyesno("Logout", "Are you sure you want to logout?"):
            user_service.logout()
            self.root.destroy()
    
    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit?"):
            self.root.destroy()
            sys.exit()
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{APP_CONFIG['title']}
Version: {APP_CONFIG['version']}

A comprehensive inventory management system
with user management, product tracking, and reporting.

Developed with Python and Tkinter
        """
        messagebox.showinfo("About", about_text)
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def run_main_application():
    """Run the main application"""
    app = InventoryManagementApp()
    app.run()

if __name__ == "__main__":
    # This would normally be called from main.py after login
    run_main_application()
