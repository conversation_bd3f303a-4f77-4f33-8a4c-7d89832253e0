# Direct test of dialog Save button

import tkinter as tk
from tkinter import ttk
from database import db
from user_management import user_service

def test_user_dialog_directly():
    """Test UserDialog directly"""
    print("Testing UserDialog Save button directly...")
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    # Create main window
    root = tk.Tk()
    root.title("Direct Dialog Test")
    root.geometry("400x300")
    
    # Add instructions
    instructions = tk.Text(root, height=10, width=50)
    instructions.pack(pady=10)
    instructions.insert('1.0', """Direct Dialog Test Instructions:

1. Click 'Open User Dialog' button below
2. Look for Save and Cancel buttons at the bottom
3. If you don't see Save button, there's an issue
4. Fill in the form and try clicking Save
5. Check if the dialog closes and user is created

Expected behavior:
- Save button should be visible at bottom right
- Cancel button should be next to Save button
- Save button should validate and save data
""")
    instructions.config(state='disabled')
    
    def open_user_dialog():
        """Open UserDialog for testing"""
        try:
            from dialogs import UserDialog
            print("📋 Opening UserDialog...")
            dialog = UserDialog(root, "Add User - Test")
            print("✅ UserDialog opened - Check for Save button!")
            
            # Wait for dialog to close and check result
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                print(f"✅ Dialog returned result: {dialog.result}")
            else:
                print("❌ Dialog was cancelled or no result")
                
        except Exception as e:
            print(f"❌ Error opening UserDialog: {e}")
            import traceback
            traceback.print_exc()
    
    def open_simple_test_dialog():
        """Open a simple test dialog with guaranteed Save button"""
        test_dialog = tk.Toplevel(root)
        test_dialog.title("Simple Test Dialog")
        test_dialog.geometry("300x200")
        test_dialog.transient(root)
        test_dialog.grab_set()
        
        # Main frame
        main_frame = ttk.Frame(test_dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Content
        ttk.Label(main_frame, text="Simple Test Dialog", font=('Arial', 12)).pack(pady=10)
        ttk.Label(main_frame, text="This dialog should have Save and Cancel buttons").pack(pady=5)
        
        # Entry field
        ttk.Label(main_frame, text="Test Field:").pack(anchor='w')
        test_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=test_var, width=25).pack(pady=5)
        
        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
        
        def save_action():
            print(f"✅ Save clicked! Value: {test_var.get()}")
            test_dialog.destroy()
        
        def cancel_action():
            print("❌ Cancel clicked!")
            test_dialog.destroy()
        
        # Buttons
        ttk.Button(button_frame, text="Cancel", command=cancel_action).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Save", command=save_action).pack(side=tk.RIGHT)
        
        print("✅ Simple test dialog created with Save/Cancel buttons")
    
    # Test buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(pady=10)
    
    ttk.Button(button_frame, text="Open User Dialog", 
              command=open_user_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Open Simple Test Dialog", 
              command=open_simple_test_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Close", 
              command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    print("✅ Test window created")
    print("📋 Click 'Open User Dialog' to test the actual UserDialog")
    print("📋 Click 'Open Simple Test Dialog' to see a working Save button")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()

if __name__ == "__main__":
    test_user_dialog_directly()
