# Dashboard GUI for Inventory Management System

import tkinter as tk
from tkinter import ttk
from user_management import user_service
from product_management import product_service
from report_management import report_service
from config import GUI_CONFIG, USER_ROLES
# Optional matplotlib for charts
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.patches as patches
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class DashboardFrame:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        
        # Create dashboard content
        self.create_dashboard()
        
        # Refresh data
        self.refresh_data()
    
    def create_dashboard(self):
        """Create dashboard layout"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                        pady=GUI_CONFIG['padding']['medium'])
        
        current_user = user_service.get_current_user()
        welcome_text = f"Welcome, {current_user.full_name} ({current_user.role.replace('_', ' ').title()})"
        
        ttk.Label(title_frame, text=welcome_text, 
                 font=GUI_CONFIG['fonts']['heading']).pack(side=tk.LEFT)
        
        # Refresh button
        ttk.Button(title_frame, text="Refresh", 
                  command=self.refresh_data).pack(side=tk.RIGHT)
        
        # Create main content area with scrollable frame
        self.create_scrollable_content()
        
        # Statistics cards
        self.create_stats_cards()
        
        # Charts section
        self.create_charts_section()
        
        # Recent activity section
        self.create_recent_activity()
    
    def create_scrollable_content(self):
        """Create scrollable content area"""
        # Create canvas and scrollbar
        canvas = tk.Canvas(self.frame, bg=GUI_CONFIG['colors']['background'])
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def create_stats_cards(self):
        """Create statistics cards"""
        stats_frame = ttk.LabelFrame(self.scrollable_frame, text="System Overview", 
                                    padding=GUI_CONFIG['padding']['medium'])
        stats_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                        pady=GUI_CONFIG['padding']['small'])
        
        # Create grid for stats cards
        self.stats_vars = {}
        
        # Product statistics
        product_frame = ttk.Frame(stats_frame)
        product_frame.grid(row=0, column=0, padx=GUI_CONFIG['padding']['small'], 
                          pady=GUI_CONFIG['padding']['small'], sticky='ew')
        
        ttk.Label(product_frame, text="Products", 
                 font=GUI_CONFIG['fonts']['heading']).pack()
        
        self.stats_vars['total_products'] = tk.StringVar(value="Loading...")
        ttk.Label(product_frame, textvariable=self.stats_vars['total_products'], 
                 font=GUI_CONFIG['fonts']['default']).pack()
        
        # Low stock alerts
        alert_frame = ttk.Frame(stats_frame)
        alert_frame.grid(row=0, column=1, padx=GUI_CONFIG['padding']['small'], 
                        pady=GUI_CONFIG['padding']['small'], sticky='ew')
        
        ttk.Label(alert_frame, text="Low Stock Alerts", 
                 font=GUI_CONFIG['fonts']['heading']).pack()
        
        self.stats_vars['low_stock'] = tk.StringVar(value="Loading...")
        low_stock_label = ttk.Label(alert_frame, textvariable=self.stats_vars['low_stock'], 
                                   font=GUI_CONFIG['fonts']['default'])
        low_stock_label.pack()
        
        # Inventory value
        value_frame = ttk.Frame(stats_frame)
        value_frame.grid(row=0, column=2, padx=GUI_CONFIG['padding']['small'], 
                        pady=GUI_CONFIG['padding']['small'], sticky='ew')
        
        ttk.Label(value_frame, text="Total Inventory Value", 
                 font=GUI_CONFIG['fonts']['heading']).pack()
        
        self.stats_vars['inventory_value'] = tk.StringVar(value="Loading...")
        ttk.Label(value_frame, textvariable=self.stats_vars['inventory_value'], 
                 font=GUI_CONFIG['fonts']['default']).pack()
        
        # Categories
        category_frame = ttk.Frame(stats_frame)
        category_frame.grid(row=1, column=0, padx=GUI_CONFIG['padding']['small'], 
                           pady=GUI_CONFIG['padding']['small'], sticky='ew')
        
        ttk.Label(category_frame, text="Categories", 
                 font=GUI_CONFIG['fonts']['heading']).pack()
        
        self.stats_vars['total_categories'] = tk.StringVar(value="Loading...")
        ttk.Label(category_frame, textvariable=self.stats_vars['total_categories'], 
                 font=GUI_CONFIG['fonts']['default']).pack()
        
        # Out of stock
        out_stock_frame = ttk.Frame(stats_frame)
        out_stock_frame.grid(row=1, column=1, padx=GUI_CONFIG['padding']['small'], 
                            pady=GUI_CONFIG['padding']['small'], sticky='ew')
        
        ttk.Label(out_stock_frame, text="Out of Stock", 
                 font=GUI_CONFIG['fonts']['heading']).pack()
        
        self.stats_vars['out_of_stock'] = tk.StringVar(value="Loading...")
        out_stock_label = ttk.Label(out_stock_frame, textvariable=self.stats_vars['out_of_stock'], 
                                   font=GUI_CONFIG['fonts']['default'])
        out_stock_label.pack()
        
        # Configure grid weights
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(2, weight=1)
    
    def create_charts_section(self):
        """Create charts section"""
        charts_frame = ttk.LabelFrame(self.scrollable_frame, text="Analytics",
                                     padding=GUI_CONFIG['padding']['medium'])
        charts_frame.pack(fill=tk.BOTH, expand=True,
                         padx=GUI_CONFIG['padding']['medium'],
                         pady=GUI_CONFIG['padding']['small'])

        if MATPLOTLIB_AVAILABLE:
            # Create matplotlib figure
            self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 5))
            self.fig.patch.set_facecolor(GUI_CONFIG['colors']['background'])

            # Create canvas for matplotlib
            self.chart_canvas = FigureCanvasTkAgg(self.fig, charts_frame)
            self.chart_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        else:
            # Show message if matplotlib is not available
            ttk.Label(charts_frame, text="Charts require matplotlib. Install with: pip install matplotlib",
                     font=GUI_CONFIG['fonts']['default']).pack(expand=True)
    
    def create_recent_activity(self):
        """Create recent activity section"""
        activity_frame = ttk.LabelFrame(self.scrollable_frame, text="Recent Activity", 
                                       padding=GUI_CONFIG['padding']['medium'])
        activity_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                           pady=GUI_CONFIG['padding']['small'])
        
        # Create treeview for recent transactions
        columns = ('Date', 'Product', 'Type', 'Quantity', 'User')
        self.activity_tree = ttk.Treeview(activity_frame, columns=columns, 
                                         show='headings', height=8)
        
        # Configure columns
        for col in columns:
            self.activity_tree.heading(col, text=col)
            self.activity_tree.column(col, width=120)
        
        # Add scrollbar
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient=tk.VERTICAL, 
                                          command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def refresh_data(self):
        """Refresh dashboard data"""
        try:
            # Get product statistics
            product_stats = product_service.get_product_stats()
            
            # Update statistics
            self.stats_vars['total_products'].set(str(product_stats.get('total_products', 0)))
            self.stats_vars['total_categories'].set(str(product_stats.get('total_categories', 0)))
            self.stats_vars['low_stock'].set(str(product_stats.get('low_stock_count', 0)))
            self.stats_vars['out_of_stock'].set(str(product_stats.get('out_of_stock_count', 0)))
            
            # Format inventory value
            inventory_value = product_stats.get('total_inventory_value', 0)
            self.stats_vars['inventory_value'].set(f"${inventory_value:,.2f}")
            
            # Update charts
            self.update_charts()
            
            # Update recent activity
            self.update_recent_activity()
            
        except Exception as e:
            print(f"Error refreshing dashboard data: {e}")
    
    def update_charts(self):
        """Update dashboard charts"""
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            # Clear previous charts
            self.ax1.clear()
            self.ax2.clear()
            
            # Chart 1: Stock Status Distribution
            low_stock_products = product_service.get_low_stock_products()
            out_of_stock_products = product_service.get_out_of_stock_products()
            all_products = product_service.get_all_products()
            
            in_stock = len(all_products) - len(low_stock_products)
            low_stock = len(low_stock_products) - len(out_of_stock_products)
            out_of_stock = len(out_of_stock_products)
            
            labels = ['In Stock', 'Low Stock', 'Out of Stock']
            sizes = [in_stock, low_stock, out_of_stock]
            colors = [GUI_CONFIG['colors']['success'], 
                     GUI_CONFIG['colors']['warning'], 
                     GUI_CONFIG['colors']['secondary']]
            
            if sum(sizes) > 0:
                self.ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', 
                            startangle=90)
                self.ax1.set_title('Stock Status Distribution')
            else:
                self.ax1.text(0.5, 0.5, 'No Data Available', 
                             ha='center', va='center', transform=self.ax1.transAxes)
                self.ax1.set_title('Stock Status Distribution')
            
            # Chart 2: Top Categories by Product Count
            categories = product_service.get_all_categories()
            category_counts = []
            category_names = []
            
            for category in categories[:5]:  # Top 5 categories
                products_in_category = product_service.get_products_by_category(category.id)
                if products_in_category:
                    category_counts.append(len(products_in_category))
                    category_names.append(category.name)
            
            if category_counts:
                bars = self.ax2.bar(category_names, category_counts, 
                                   color=GUI_CONFIG['colors']['primary'])
                self.ax2.set_title('Top Categories by Product Count')
                self.ax2.set_ylabel('Number of Products')
                
                # Rotate x-axis labels if needed
                if len(max(category_names, key=len)) > 8:
                    self.ax2.tick_params(axis='x', rotation=45)
            else:
                self.ax2.text(0.5, 0.5, 'No Categories Available', 
                             ha='center', va='center', transform=self.ax2.transAxes)
                self.ax2.set_title('Top Categories by Product Count')
            
            # Adjust layout and refresh
            self.fig.tight_layout()
            self.chart_canvas.draw()
            
        except Exception as e:
            print(f"Error updating charts: {e}")
    
    def update_recent_activity(self):
        """Update recent activity list"""
        try:
            # Clear existing items
            for item in self.activity_tree.get_children():
                self.activity_tree.delete(item)
            
            # Get recent transactions
            from models import Transaction
            recent_transactions = Transaction.get_recent(20)
            
            for transaction in recent_transactions:
                # Get product and user details
                product = product_service.get_product_by_id(transaction.product_id)
                user = user_service.get_current_user()  # This should be improved to get actual user
                
                if product:
                    # Format date
                    date_str = str(transaction.created_at)[:16] if transaction.created_at else "Unknown"
                    
                    # Insert into treeview
                    self.activity_tree.insert('', 'end', values=(
                        date_str,
                        product.name,
                        transaction.transaction_type,
                        transaction.quantity,
                        user.full_name if user else "Unknown"
                    ))
            
        except Exception as e:
            print(f"Error updating recent activity: {e}")
    
    def get_frame(self):
        """Get the main frame"""
        return self.frame
