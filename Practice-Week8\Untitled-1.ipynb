{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0cf7db4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name,Age,City\n", "Alice,30,New York\n", "<PERSON>,24,London\n"]}], "source": ["# Step 1: Define the structured data\n", "data = [\n", "    ['Name', 'Age', 'City'],\n", "    ['<PERSON>', 30, 'New York'],\n", "    ['<PERSON>', 24, 'London']\n", "]\n", "\n", "# Step 2–5: Convert each row to a CSV line and print\n", "for row in data:\n", "    # Convert each element to a string and join with commas\n", "    csv_line = ','.join(str(item) for item in row)\n", "    print(csv_line)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "27e6a656", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["salt, sugar  , gallec\n", "['salt', ' sugar  ', ' gallec']\n"]}], "source": ["ingredients_input = \"salt, sugar  , gallec\"\n", "print(ingredients_input)\n", "print(ingredients_input.split(\",\"))"]}, {"cell_type": "code", "execution_count": 1, "id": "502ff6b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Name   | Price  | Stock\n", "----------+--------+------\n", "Keyboard  |  75.99 |   150\n", "Mouse     |  25.00 |   300\n", "Monitor   | 210.50 |    75\n", "USB Cable |   5.49 |   500\n"]}], "source": ["# Step 1: Given data\n", "products = [\n", "    {'name': 'Keyboard', 'price': 75.99, 'stock': 150},\n", "    {'name': '<PERSON>', 'price': 25.00, 'stock': 300},\n", "    {'name': 'Monitor', 'price': 210.50, 'stock': 75},\n", "    {'name': 'USB Cable', 'price': 5.49, 'stock': 500}\n", "]\n", "\n", "# Step 2: Determine max width for each column\n", "name_width = max(len(str(p['name'])) for p in products)\n", "price_width = max(len(f\"{p['price']:.2f}\") for p in products)\n", "stock_width = max(len(str(p['stock'])) for p in products)\n", "\n", "# Also consider the header labels\n", "name_width = max(name_width, len(\"Name\"))\n", "price_width = max(price_width, len(\"Price\"))\n", "stock_width = max(stock_width, len(\"Stock\"))\n", "\n", "# Step 3: Print the header row\n", "header = f\"{'Name'.center(name_width)} | {'Price'.center(price_width)} | {'Stock'.center(stock_width)}\"\n", "print(header)\n", "\n", "# Step 4: Print separator line\n", "print(f\"{'-'*name_width}-+-{'-'*price_width}-+-{'-'*stock_width}\")\n", "\n", "# Step 5: Print each row with proper formatting\n", "for product in products:\n", "    name = product['name'].ljust(name_width)\n", "    price = f\"{product['price']:.2f}\".rjust(price_width)\n", "    stock = str(product['stock']).rjust(stock_width)\n", "    print(f\"{name} | {price} | {stock}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}