# Setup script for Inventory Management System

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install required packages"""
    print("\nInstalling required packages...")
    
    try:
        # Check if requirements.txt exists
        if not os.path.exists('requirements.txt'):
            print("❌ requirements.txt not found")
            return False
        
        # Install packages
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All packages installed successfully")
            return True
        else:
            print("❌ Package installation failed:")
            print(result.stderr)
            return False
    
    except Exception as e:
        print(f"❌ Error installing packages: {e}")
        return False

def test_imports():
    """Test if all required modules can be imported"""
    print("\nTesting module imports...")
    
    required_modules = [
        'tkinter',
        'sqlite3',
        'hashlib',
        'datetime'
    ]
    
    optional_modules = [
        ('mysql.connector', 'MySQL support'),
        ('psycopg2', 'PostgreSQL support'),
        ('matplotlib', 'Charts and visualization')
    ]
    
    # Test required modules
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - REQUIRED")
            return False
    
    # Test optional modules
    for module, description in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"⚠️  {module} - {description} (optional)")
    
    return True

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if platform.system() != 'Windows':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Inventory Management System.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created")
    except ImportError:
        print("⚠️  Desktop shortcut creation requires pywin32 (optional)")
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")

def run_system_test():
    """Run system functionality test"""
    print("\nRunning system functionality test...")
    
    try:
        result = subprocess.run([sys.executable, 'test_system.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ System test passed")
            return True
        else:
            print("❌ System test failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    
    except Exception as e:
        print(f"❌ Error running system test: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("INVENTORY MANAGEMENT SYSTEM - SETUP")
    print("=" * 60)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing requirements", install_requirements),
        ("Testing imports", test_imports),
        ("Running system test", run_system_test)
    ]
    
    success_count = 0
    
    for step_name, step_function in steps:
        print(f"\n{step_name}...")
        try:
            if step_function():
                success_count += 1
            else:
                print(f"❌ {step_name} failed")
        except Exception as e:
            print(f"❌ {step_name} error: {e}")
    
    # Optional steps
    print("\nOptional setup steps...")
    create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("SETUP COMPLETE")
    print("=" * 60)
    
    if success_count == len(steps):
        print("🎉 Setup completed successfully!")
        print("\nTo start the application, run:")
        print("  python main.py")
        print("\nDefault login credentials:")
        print("  Username: admin")
        print("  Password: admin123")
    else:
        print(f"⚠️  Setup completed with {len(steps) - success_count} issues")
        print("Please check the output above and resolve any problems")
    
    print("\nFor help and documentation, see README.md")

if __name__ == "__main__":
    main()
