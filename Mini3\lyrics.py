import sys
from time import sleep
import time

def printLyrics():
    print()
    print("-" * 10)
    lines = [
        ("Hello,", 0.04),
        ("My name is", 0.05, "Kong Usa", 0.6),
        ("Kong Usa", 0.05),
        
    ]

    delays = [0.5, 1.3, 0.3]

    for i, (line, char_delay) in enumerate(lines):
        for char in line:
            print(char, end='')
            sys.stdout.flush()
            sleep(char_delay)
        print()
        time.sleep(delays[i])

printLyrics()
