import tkinter as tk
from tkinter import messagebox

def calculate_bmi():
    """Calculate and display BMI"""
    try:
        height = float(height_entry.get())
        weight = float(weight_entry.get())
        
        if height <= 0 or weight <= 0:
            messagebox.showerror("Error", "Please enter positive numbers!")
            return
            
        bmi = weight / ((height / 100) ** 2)
        
        # Determine category and color
        if bmi < 18.5:
            category, color = "Underweight", "#f39c12"
        elif bmi < 25:
            category, color = "Normal", "#27ae60"
        elif bmi < 30:
            category, color = "Overweight", "#f1c40f"
        else:
            category, color = "Obese", "#e74c3c"
        
        # Update result
        result_label.config(text=f"BMI: {bmi:.1f}\n{category}", 
                           fg=color, font=("Arial", 16, "bold"))
        
    except ValueError:
        messagebox.showerror("Error", "Please enter valid numbers!")

def clear_all():
    """Clear all fields"""
    height_entry.delete(0, tk.END)
    weight_entry.delete(0, tk.END)
    result_label.config(text="Enter your details", fg="gray")

# Create main window
root = tk.Tk()
root.title("BMI Calculator")
root.geometry("350x400")
root.configure(bg="#f0f0f0")

# Title
tk.Label(root, text="🏥 BMI Calculator", bg="#f0f0f0", fg="#2c3e50",
         font=("Arial", 20, "bold")).pack(pady=20)

# Input frame
input_frame = tk.Frame(root, bg="white", relief="ridge", bd=2)
input_frame.pack(padx=20, pady=10, fill="x")

# Height input
tk.Label(input_frame, text="Height (cm):", bg="white", 
         font=("Arial", 12, "bold")).pack(pady=(15, 5))
height_entry = tk.Entry(input_frame, font=("Arial", 12), justify="center")
height_entry.pack(pady=(0, 10), padx=20, fill="x")

# Weight input
tk.Label(input_frame, text="Weight (kg):", bg="white", 
         font=("Arial", 12, "bold")).pack(pady=5)
weight_entry = tk.Entry(input_frame, font=("Arial", 12), justify="center")
weight_entry.pack(pady=(0, 15), padx=20, fill="x")

# Buttons
button_frame = tk.Frame(root, bg="#f0f0f0")
button_frame.pack(pady=15)

tk.Button(button_frame, text="Calculate", command=calculate_bmi,
          bg="#3498db", fg="white", font=("Arial", 12, "bold"),
          padx=20, pady=5, cursor="hand2").pack(side="left", padx=5)

tk.Button(button_frame, text="Clear", command=clear_all,
          bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
          padx=20, pady=5, cursor="hand2").pack(side="left", padx=5)

# Result display
result_frame = tk.Frame(root, bg="white", relief="ridge", bd=2)
result_frame.pack(padx=20, pady=10, fill="x")

result_label = tk.Label(result_frame, text="Enter your details", bg="white",
                       fg="gray", font=("Arial", 14), pady=20)
result_label.pack()

# BMI categories (simple table)
info_frame = tk.Frame(root, bg="#ecf0f1", relief="groove", bd=1)
info_frame.pack(padx=20, pady=10, fill="x")

tk.Label(info_frame, text="BMI Categories", bg="#ecf0f1", fg="#2c3e50",
         font=("Arial", 10, "bold")).pack(pady=(10, 5))

categories = "< 18.5: Underweight | 18.5-24.9: Normal | 25-29.9: Overweight | ≥30: Obese"
tk.Label(info_frame, text=categories, bg="#ecf0f1", fg="#7f8c8d",
         font=("Arial", 8), wraplength=300).pack(pady=(0, 10))

# Bind Enter key
root.bind('<Return>', lambda e: calculate_bmi())

# Start the app
root.mainloop()
