# Final test to verify data capture is working

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service
from product_management import product_service

def final_test():
    """Final comprehensive test of data capture"""
    print("=" * 60)
    print("FINAL DATA CAPTURE TEST")
    print("=" * 60)
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    print("✅ Database initialized and authenticated")
    
    # Create main window
    root = tk.Tk()
    root.title("Final Data Capture Test")
    root.geometry("600x500")
    
    # Create header
    header_frame = ttk.Frame(root)
    header_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Label(header_frame, text="Final Data Capture Test", 
             font=('Arial', 16, 'bold')).pack()
    
    ttk.Label(header_frame, text="Test if input data is properly captured and saved", 
             font=('Arial', 10)).pack(pady=5)
    
    # Create results area
    results_frame = ttk.LabelFrame(root, text="Test Results", padding=10)
    results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    results_text = tk.Text(results_frame, height=20, width=70, font=('Courier', 9))
    scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
    results_text.configure(yscrollcommand=scrollbar.set)
    
    results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_result(message):
        """Log a result to the text area"""
        results_text.insert(tk.END, message + "\n")
        results_text.see(tk.END)
        root.update()
    
    def test_category_creation():
        """Test category creation end-to-end"""
        log_result("🔄 Testing Category Creation...")
        log_result("Opening CategoryDialog...")
        
        try:
            from dialogs import CategoryDialog
            dialog = CategoryDialog(root, "Test Category Creation")
            
            log_result("✅ CategoryDialog opened successfully")
            log_result("📋 Please fill in:")
            log_result("   Name: Final Test Category")
            log_result("   Description: This is a final test category")
            log_result("   Then click Save button")
            log_result("")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                name = dialog.result.get('name', '')
                description = dialog.result.get('description', '')
                
                log_result("✅ DATA CAPTURED SUCCESSFULLY!")
                log_result(f"   Name: '{name}'")
                log_result(f"   Description: '{description}'")
                log_result("")
                
                if name:
                    # Try to save to database
                    try:
                        success = product_service.create_category(
                            name=name,
                            description=description
                        )
                        
                        if success:
                            log_result("✅ CATEGORY SAVED TO DATABASE!")
                            log_result("   Category creation completely successful")
                            
                            # Verify it was saved
                            categories = product_service.get_all_categories()
                            found = any(cat.name == name for cat in categories)
                            if found:
                                log_result("✅ VERIFICATION: Category found in database")
                            else:
                                log_result("❌ VERIFICATION: Category not found in database")
                        else:
                            log_result("❌ Failed to save category to database")
                    
                    except ValueError as e:
                        if "already exists" in str(e):
                            log_result("⚠️  Category already exists (this is expected if running multiple times)")
                        else:
                            log_result(f"❌ Database error: {e}")
                    except Exception as e:
                        log_result(f"❌ Unexpected error: {e}")
                else:
                    log_result("❌ Name is empty - data not captured properly")
            else:
                log_result("❌ NO DATA CAPTURED")
                log_result("   Dialog result is None or empty")
            
            log_result("-" * 50)
            
        except Exception as e:
            log_result(f"❌ Error in category test: {e}")
            import traceback
            log_result(traceback.format_exc())
    
    def test_user_creation():
        """Test user creation end-to-end"""
        log_result("🔄 Testing User Creation...")
        log_result("Opening UserDialog...")
        
        try:
            from dialogs import UserDialog
            dialog = UserDialog(root, "Test User Creation")
            
            log_result("✅ UserDialog opened successfully")
            log_result("📋 Please fill in:")
            log_result("   Username: finaltest")
            log_result("   Full Name: Final Test User")
            log_result("   Email: <EMAIL>")
            log_result("   Role: Supplier")
            log_result("   Password: test123")
            log_result("   Confirm Password: test123")
            log_result("   Then click Save button")
            log_result("")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                username = dialog.result.get('username', '')
                full_name = dialog.result.get('full_name', '')
                email = dialog.result.get('email', '')
                role = dialog.result.get('role', '')
                
                log_result("✅ DATA CAPTURED SUCCESSFULLY!")
                log_result(f"   Username: '{username}'")
                log_result(f"   Full Name: '{full_name}'")
                log_result(f"   Email: '{email}'")
                log_result(f"   Role: '{role}'")
                log_result("")
                
                if username and full_name and email:
                    # Try to save to database
                    try:
                        success = user_service.create_user(
                            username=username,
                            password=dialog.result.get('password', ''),
                            email=email,
                            role=role,
                            full_name=full_name
                        )
                        
                        if success:
                            log_result("✅ USER SAVED TO DATABASE!")
                            log_result("   User creation completely successful")
                        else:
                            log_result("❌ Failed to save user to database")
                    
                    except ValueError as e:
                        if "already exists" in str(e):
                            log_result("⚠️  User already exists (this is expected if running multiple times)")
                        else:
                            log_result(f"❌ Database error: {e}")
                    except Exception as e:
                        log_result(f"❌ Unexpected error: {e}")
                else:
                    log_result("❌ Required fields are empty - data not captured properly")
            else:
                log_result("❌ NO DATA CAPTURED")
                log_result("   Dialog result is None or empty")
            
            log_result("-" * 50)
            
        except Exception as e:
            log_result(f"❌ Error in user test: {e}")
            import traceback
            log_result(traceback.format_exc())
    
    def run_all_tests():
        """Run all tests"""
        log_result("=" * 60)
        log_result("RUNNING ALL DATA CAPTURE TESTS")
        log_result("=" * 60)
        log_result("")
        
        test_category_creation()
        test_user_creation()
        
        log_result("=" * 60)
        log_result("ALL TESTS COMPLETED")
        log_result("=" * 60)
        log_result("")
        log_result("If data was captured and saved successfully,")
        log_result("the input data capture issue is FIXED!")
    
    # Create test buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Button(button_frame, text="Test Category Creation", 
              command=test_category_creation).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Test User Creation", 
              command=test_user_creation).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Run All Tests", 
              command=run_all_tests).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(button_frame, text="Close", 
              command=root.destroy).pack(side=tk.RIGHT, padx=5)
    
    # Initial log
    log_result("🧪 FINAL DATA CAPTURE TEST READY")
    log_result("=" * 50)
    log_result("This test will verify that input data is properly captured")
    log_result("from dialog forms and saved to the database.")
    log_result("")
    log_result("Click test buttons to begin testing.")
    log_result("Fill in the forms completely and click Save.")
    log_result("Check results below to see if data was captured.")
    log_result("")
    
    print("✅ Final test window opened")
    print("📋 Use the GUI to test data capture functionality")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()

if __name__ == "__main__":
    final_test()
