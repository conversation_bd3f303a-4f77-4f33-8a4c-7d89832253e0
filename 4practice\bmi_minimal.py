import tkinter as tk
from tkinter import messagebox

def calculate():
    try:
        h, w = float(height.get()), float(weight.get())
        if h <= 0 or w <= 0:
            raise ValueError

        bmi = w / (h/100)**2

        if bmi < 16:
            category, color, bg = "Severely Underweight", "#c0392b", "#fadbd8"
            risk = "Very High Risk"
            advice = "Immediate medical consultation required. Severe malnutrition possible."
            ideal_range = "Gain weight through balanced nutrition and medical supervision."
        elif bmi < 18.5:
            category, color, bg = "Underweight", "#e67e22", "#fdf2e9"
            risk = "Moderate Risk"
            advice = "Consider increasing caloric intake with nutritious foods."
            ideal_range = "Focus on healthy weight gain through proper diet and exercise."
        elif bmi < 25:
            category, color, bg = "Normal Weight", "#27ae60", "#eafaf1"
            risk = "Low Risk"
            advice = "Excellent! Maintain your current healthy lifestyle."
            ideal_range = "Continue balanced diet and regular physical activity."
        elif bmi < 30:
            category, color, bg = "Overweight", "#f39c12", "#fef9e7"
            risk = "Moderate Risk"
            advice = "Consider weight loss through diet and exercise."
            ideal_range = "Aim for 5-10% weight reduction through lifestyle changes."
        elif bmi < 35:
            category, color, bg = "Obese Class I", "#e74c3c", "#fdedec"
            risk = "High Risk"
            advice = "Weight loss recommended. Consider professional guidance."
            ideal_range = "Structured weight loss program with medical supervision advised."
        elif bmi < 40:
            category, color, bg = "Obese Class II", "#c0392b", "#fadbd8"
            risk = "Very High Risk"
            advice = "Significant health risks. Medical intervention recommended."
            ideal_range = "Intensive weight management program with healthcare team."
        else:
            category, color, bg = "Obese Class III", "#922b21", "#ec7063"
            risk = "Extremely High Risk"
            advice = "Immediate medical attention required for weight management."
            ideal_range = "Comprehensive medical treatment plan essential."

        # Calculate ideal weight range
        ideal_min = 18.5 * (h/100)**2
        ideal_max = 24.9 * (h/100)**2

        # Update displays
        result.config(text=f"{bmi:.1f}", fg=color, bg=bg, font=("Arial", 26, "bold"))
        category_label.config(text=category, fg=color, bg=bg, font=("Arial", 11, "bold"))

        # Update detailed information
        update_details(bmi, category, risk, advice, ideal_range, ideal_min, ideal_max, h, w)

    except:
        messagebox.showerror("❌ Error", "Please enter valid positive numbers!")

def update_details(bmi, category, risk, advice, ideal_range, ideal_min, ideal_max, height, weight):
    """Update the detailed information section"""

    # Health risk
    risk_detail.config(text=f"Health Risk: {risk}")

    # Advice
    advice_detail.config(text=f"💡 Advice: {advice}")

    # Ideal weight range
    ideal_detail.config(text=f"📊 Ideal Weight Range: {ideal_min:.1f} - {ideal_max:.1f} kg")

    # BMI calculation breakdown
    height_m = height / 100
    calc_detail.config(text=f"📐 Calculation: {weight}kg ÷ ({height_m:.2f}m)² = {bmi:.2f}")

    # Weight difference
    if bmi < 18.5:
        weight_diff = ideal_min - weight
        diff_detail.config(text=f"⬆️ Consider gaining: {weight_diff:.1f} kg", fg="#e67e22")
    elif bmi > 25:
        weight_diff = weight - ideal_max
        diff_detail.config(text=f"⬇️ Consider losing: {weight_diff:.1f} kg", fg="#e74c3c")
    else:
        diff_detail.config(text="✅ Weight is in healthy range!", fg="#27ae60")

def clear_fields():
    height.delete(0, tk.END)
    weight.delete(0, tk.END)
    result.config(text="--", fg="#95a5a6", bg="#ecf0f1", font=("Arial", 26, "bold"))
    category_label.config(text="Enter your details", fg="#95a5a6", bg="#ecf0f1")

    # Clear detailed information
    risk_detail.config(text="Health Risk: --")
    advice_detail.config(text="💡 Advice: Enter your height and weight above")
    ideal_detail.config(text="📊 Ideal Weight Range: --")
    calc_detail.config(text="📐 Calculation: --")
    diff_detail.config(text="Weight Status: --", fg="#7f8c8d")

# Create modern window
root = tk.Tk()
root.title("🏥 BMI Calculator")
root.geometry("400x600")
root.configure(bg="#f8f9fa")
root.resizable(True, True)
root.minsize(350, 500)

# Header with gradient effect
header = tk.Frame(root, bg="#3498db", height=60)
header.pack(fill="x")
header.pack_propagate(False)

tk.Label(header, text="🏥 BMI Calculator", bg="#3498db", fg="white",
         font=("Arial", 16, "bold")).pack(expand=True)

# Main container
main = tk.Frame(root, bg="#f8f9fa")
main.pack(expand=True, fill="both", padx=25, pady=20)

# Input section with cards
input_card = tk.Frame(main, bg="white", relief="solid", bd=1)
input_card.pack(fill="x", pady=(0, 18))

tk.Label(input_card, text="📏 Height (cm)", bg="white", fg="#2c3e50",
         font=("Arial", 11, "bold")).pack(pady=(18, 5))
height = tk.Entry(input_card, font=("Arial", 12), justify="center",
                  relief="flat", bg="#f8f9fa", fg="#2c3e50")
height.pack(pady=(0, 15), padx=20, ipady=8, fill="x")

tk.Label(input_card, text="⚖️ Weight (kg)", bg="white", fg="#2c3e50",
         font=("Arial", 11, "bold")).pack(pady=5)
weight = tk.Entry(input_card, font=("Arial", 12), justify="center",
                  relief="flat", bg="#f8f9fa", fg="#2c3e50")
weight.pack(pady=(0, 18), padx=20, ipady=8, fill="x")

# Buttons
btn_frame = tk.Frame(main, bg="#f8f9fa")
btn_frame.pack(pady=15)

calc_btn = tk.Button(btn_frame, text="🧮 Calculate", command=calculate,
                     bg="#27ae60", fg="white", font=("Arial", 11, "bold"),
                     relief="flat", padx=25, pady=10, cursor="hand2")
calc_btn.pack(side="left", padx=8)

clear_btn = tk.Button(btn_frame, text="🗑️ Clear", command=clear_fields,
                      bg="#e74c3c", fg="white", font=("Arial", 11, "bold"),
                      relief="flat", padx=25, pady=10, cursor="hand2")
clear_btn.pack(side="left", padx=8)

# Result display card
result_card = tk.Frame(main, bg="#ecf0f1", relief="solid", bd=1, height=110)
result_card.pack(fill="x", pady=18)
result_card.pack_propagate(False)

tk.Label(result_card, text="Your BMI", bg="#ecf0f1", fg="#7f8c8d",
         font=("Arial", 10, "bold")).pack(pady=(15, 5))

result = tk.Label(result_card, text="--", bg="#ecf0f1", fg="#95a5a6",
                  font=("Arial", 26, "bold"))
result.pack(pady=5)

category_label = tk.Label(result_card, text="Enter your details", bg="#ecf0f1",
                         fg="#95a5a6", font=("Arial", 11))
category_label.pack(pady=(5, 15))

# Detailed information section
detail_frame = tk.Frame(main, bg="white", relief="solid", bd=1)
detail_frame.pack(fill="both", expand=True, pady=18)

tk.Label(detail_frame, text="📋 Detailed Information", bg="white", fg="#2c3e50",
         font=("Arial", 11, "bold")).pack(pady=(15, 12))

# Create scrollable frame for details
canvas = tk.Canvas(detail_frame, bg="white", highlightthickness=0)
scrollbar = tk.Scrollbar(detail_frame, orient="vertical", command=canvas.yview)
scrollable_frame = tk.Frame(canvas, bg="white")

scrollable_frame.bind(
    "<Configure>",
    lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
)

canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
canvas.configure(yscrollcommand=scrollbar.set)

# Detail labels
risk_detail = tk.Label(scrollable_frame, text="Health Risk: --", bg="white", fg="#7f8c8d",
                      font=("Arial", 10, "bold"), anchor="w")
risk_detail.pack(fill="x", padx=20, pady=5)

advice_detail = tk.Label(scrollable_frame, text="💡 Advice: Enter your height and weight above",
                        bg="white", fg="#7f8c8d", font=("Arial", 10), anchor="w", wraplength=350)
advice_detail.pack(fill="x", padx=20, pady=5)

ideal_detail = tk.Label(scrollable_frame, text="📊 Ideal Weight Range: --", bg="white", fg="#7f8c8d",
                       font=("Arial", 10), anchor="w")
ideal_detail.pack(fill="x", padx=20, pady=5)

calc_detail = tk.Label(scrollable_frame, text="📐 Calculation: --", bg="white", fg="#7f8c8d",
                      font=("Arial", 10), anchor="w")
calc_detail.pack(fill="x", padx=20, pady=5)

diff_detail = tk.Label(scrollable_frame, text="Weight Status: --", bg="white", fg="#7f8c8d",
                      font=("Arial", 10, "bold"), anchor="w")
diff_detail.pack(fill="x", padx=20, pady=(5, 15))

# Pack canvas and scrollbar
canvas.pack(side="left", fill="both", expand=True, padx=(0, 0))
scrollbar.pack(side="right", fill="y")

# Mouse wheel scrolling
def on_mousewheel(event):
    canvas.yview_scroll(int(-1*(event.delta/120)), "units")

canvas.bind("<MouseWheel>", on_mousewheel)

# Quick reference
ref_frame = tk.Frame(main, bg="#ecf0f1", relief="solid", bd=1)
ref_frame.pack(fill="x", pady=(10, 0))

tk.Label(ref_frame, text="📊 BMI Categories", bg="#ecf0f1", fg="#2c3e50",
         font=("Arial", 10, "bold")).pack(pady=(12, 8))

categories = "🟠 <18.5 Underweight  🟢 18.5-24.9 Normal  🟡 25-29.9 Overweight  🔴 ≥30 Obese"
tk.Label(ref_frame, text=categories, bg="#ecf0f1", fg="#7f8c8d",
         font=("Arial", 8), wraplength=350).pack(pady=(0, 12), padx=15)

# Hover effects for buttons
def add_hover(btn, normal, hover):
    btn.bind("<Enter>", lambda _: btn.config(bg=hover))
    btn.bind("<Leave>", lambda _: btn.config(bg=normal))

add_hover(calc_btn, "#27ae60", "#2ecc71")
add_hover(clear_btn, "#e74c3c", "#ec7063")

# Bind Enter key
root.bind('<Return>', lambda _: calculate())

# Focus on height entry
height.focus_set()

root.mainloop()
