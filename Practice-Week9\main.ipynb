{"cells": [{"cell_type": "code", "execution_count": 5, "id": "ffb626d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON> has ordered 1 coffee(s).\n", " Coffee List : {'<PERSON>': 1, '<PERSON>': 0, '<PERSON>': 0, '<PERSON>': 0, '<PERSON>': 0}\n"]}], "source": ["coffee_list = {\"<PERSON>\":0, \"<PERSON>\":0, \"<PERSON>\":0, \"<PERSON>\":0, \"<PERSON>\":0}\n", "\n", "while True:\n", "    name = input(\"Enter name: \")\n", "    if name == \"\":\n", "        break\n", "    coffee_list[name] += 1\n", "    print(f\"{name} has ordered {coffee_list[name]} coffee(s).\")\n", "print(\" Coffee List :\",coffee_list)"]}, {"cell_type": "code", "execution_count": null, "id": "d232ed9b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'antwort' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 13\u001b[39m\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m lst_of_beverages:\n\u001b[32m     12\u001b[39m     antwort  = \u001b[38;5;28minput\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mShall we include \u001b[39m\u001b[33m\"\u001b[39m+ name + \u001b[33m\"\u001b[39m\u001b[33m in the list? (yes/no): \u001b[39m\u001b[33m\"\u001b[39m).lower()\n\u001b[32m---> \u001b[39m\u001b[32m13\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mantwort\u001b[49m == \u001b[33m\"\u001b[39m\u001b[33myes\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m     14\u001b[39m     lst_of_beverages[name] = {\u001b[33m\"\u001b[39m\u001b[33mcoffee\u001b[39m\u001b[33m\"\u001b[39m: \u001b[32m0\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mtea\u001b[39m\u001b[33m\"\u001b[39m: \u001b[32m0\u001b[39m}\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[31mNameError\u001b[39m: name 'antwort' is not defined"]}], "source": ["lst_of_beverages = {\n", "                    \"Usa\": {\"coffee\": 0, \"tea\": 0},\n", "                    \"Bott\": {\"coffee\": 0, \"tea\": 0},\n", "                    \"Marya\": {\"coffee\": 0, \"tea\": 0},\n", "                    }\n", "0\n", "while True:\n", "    name = input(\"Name: \").capitalize()\n", "    if name == \"\":\n", "        break\n", "    if name not in lst_of_beverages:\n", "        antwort  = input(\"Shall we include \"+ name +\n", "        \" in the list? (yes/no): \").lower()\n", "        if antwort.lower() in [\"yes\", \"y\"]:\n", "            lst_of_beverages[name] = {\"coffee\": 0, \"tea\": 0}\n", "        else:\n", "            print(name +\" Name don't have in the list.\")\n", "            continue\n", "    drink = input(\"Enter drink (coffee/tea): \").lower()\n", "    lst_of_beverages[name][drink] += 1\n", "print(lst_of_beverages)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "6ad186eb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}