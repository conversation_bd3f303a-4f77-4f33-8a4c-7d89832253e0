from abc import ABC, abstractmethod

# Abstract Class
class Shape(ABC):
    def __init__(self, width: float, height: float):
        self._width = width
        self._height = height

    # Abstract methods (must be implemented by subclasses)
    @abstractmethod
    def get_area(self) -> float:
        pass

    @abstractmethod
    def get_perimeter(self) -> float:
        pass

    # Encapsulated properties
    @property
    def width(self) -> float:
        return self._width

    @property
    def height(self) -> float:
        return self._height


# Concrete Class (Rectangle)
class Rectangle(Shape):
    def get_area(self) -> float:
        return self._width * self._height

    def get_perimeter(self) -> float:
        return 2 * (self._width + self._height)


# Concrete Class (Triangle)
class Triangle(Shape):
    def get_area(self) -> float:
        return 0.5 * self._width * self._height

    def get_perimeter(self) -> float:
        # For simplicity, assume equilateral triangle based on width
        return self._width * 3


# Usage Example
if __name__ == "__main__":
    rect = Rectangle(10, 5)
    print("Rectangle Area:", rect.get_area())
    print("Rectangle Perimeter:", rect.get_perimeter())

    tri = Triangle(6, 4)
    print("Triangle Area:", tri.get_area())
    print("Triangle Perimeter:", tri.get_perimeter())
