{"cells": [{"cell_type": "code", "execution_count": 9, "id": "fcf5a5b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["def add (a,b):\n", "    return a + b\n", "print(add(1,2))\n"]}, {"cell_type": "code", "execution_count": null, "id": "74942e0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello  World\n", "World  Unoverse\n"]}], "source": ["def classicTest(string1, string2):\n", "    print(string1,\"\",string2)\n", "classicTest(\"Hello\",\"World\")\n", "classicTest(\"World\",\"Universe\")"]}, {"cell_type": "code", "execution_count": 15, "id": "3a17558d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello  Usa\n", "Hello  Universe\n"]}], "source": ["def perfectWorld(string1, string2):\n", "    print(string1,\"\",string2)\n", "perfectWorld(\"Hello\", 'Usa')\n", "perfectWorld(\"Hello\",\"Universe\")\n"]}, {"cell_type": "code", "execution_count": 22, "id": "66dda401", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Value of x is: 1\n", "Remaining Parameters are: (2, 3, 4, 5, 6, 7, 8, 9, 10)\n", "<=><=><=><=><=><=><=><=><=><=><=><=><=><=><=><=><=><=>\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["def foo(x, *params):\n", "    print(\"Value of x is:\", x)\n", "    print(\"Remaining Parameters are:\", params)\n", "    print(\"<=>\"*18)\n", "    for p in params:\n", "        print(p)\n", "foo(1,2,3,4,5,6,7,8,9,10)"]}, {"cell_type": "code", "execution_count": 32, "id": "fb12edc2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "----------\n", "កុំលេងដី​\n", "កុំលេងដី​\n", "Hello my love\n", "Hello my love\n", "Hello my love\n", "Hello my love\n"]}, {"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mIndexError\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[32]\u001b[39m\u001b[32m, line 27\u001b[39m\n\u001b[32m     24\u001b[39m         \u001b[38;5;28mprint\u001b[39m()\n\u001b[32m     25\u001b[39m         time.sleep(delays[i])\n\u001b[32m---> \u001b[39m\u001b[32m27\u001b[39m \u001b[43mprint<PERSON><PERSON>s\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[32]\u001b[39m\u001b[32m, line 25\u001b[39m, in \u001b[36mprintLyrics\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     23\u001b[39m     sleep(char_delay)\n\u001b[32m     24\u001b[39m \u001b[38;5;28mprint\u001b[39m()\n\u001b[32m---> \u001b[39m\u001b[32m25\u001b[39m time.sleep(\u001b[43m<PERSON><PERSON>\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m)\n", "\u001b[31mIndexError\u001b[39m: list index out of range"]}], "source": ["import sys\n", "from time import sleep\n", "import time\n", "\n", "def printLyrics():\n", "    print()\n", "    print(\"-\" * 10)\n", "    lines = [\n", "        (\"កុំលេងដី​\", 0.05),\n", "        (\"កុំលេងដី​\", 0.05),\n", "        (\"Hello my love\", 0.05),\n", "        (\"Hello my love\", 0.05),\n", "        (\"Hello my love\", 0.05),\n", "        (\"Hello my love\", 0.05)\n", "    ]\n", "\n", "    delays = [0.7, 1.3, 0.3, 0.6, 0.9]  # Match delay count to lines\n", "\n", "    for i, (line, char_delay) in enumerate(lines):\n", "        for char in line:\n", "            print(char, end='')\n", "            sys.stdout.flush()\n", "            sleep(char_delay)\n", "        print()\n", "        time.sleep(delays[i])\n", "\n", "printLyrics()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}