# Data models for Inventory Management System

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List, Dict, Any
from database import db

@dataclass
class User:
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    email: str = ""
    role: str = ""
    full_name: str = ""
    created_at: Optional[datetime] = None
    is_active: bool = True
    
    def save(self) -> bool:
        """Save user to database"""
        if self.id:
            # Update existing user
            query = """UPDATE users SET username=?, email=?, role=?, full_name=?, is_active=? 
                      WHERE id=?"""
            params = (self.username, self.email, self.role, self.full_name, self.is_active, self.id)
        else:
            # Create new user
            query = """INSERT INTO users (username, password_hash, email, role, full_name, is_active) 
                      VALUES (?, ?, ?, ?, ?, ?)"""
            params = (self.username, self.password_hash, self.email, self.role, self.full_name, self.is_active)
        
        return db.execute_update(query, params)
    
    @staticmethod
    def get_by_id(user_id: int) -> Optional['User']:
        """Get user by ID"""
        result = db.execute_query("SELECT * FROM users WHERE id = ?", (user_id,))
        if result:
            return User(**result[0])
        return None
    
    @staticmethod
    def get_by_username(username: str) -> Optional['User']:
        """Get user by username"""
        result = db.execute_query("SELECT * FROM users WHERE username = ?", (username,))
        if result:
            return User(**result[0])
        return None
    
    @staticmethod
    def get_all() -> List['User']:
        """Get all users"""
        result = db.execute_query("SELECT * FROM users ORDER BY username")
        return [User(**row) for row in result] if result else []

@dataclass
class Category:
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    created_at: Optional[datetime] = None
    
    def save(self) -> bool:
        """Save category to database"""
        if self.id:
            query = "UPDATE categories SET name=?, description=? WHERE id=?"
            params = (self.name, self.description, self.id)
        else:
            query = "INSERT INTO categories (name, description) VALUES (?, ?)"
            params = (self.name, self.description)
        
        return db.execute_update(query, params)
    
    @staticmethod
    def get_all() -> List['Category']:
        """Get all categories"""
        result = db.execute_query("SELECT * FROM categories ORDER BY name")
        return [Category(**row) for row in result] if result else []
    
    @staticmethod
    def get_by_id(category_id: int) -> Optional['Category']:
        """Get category by ID"""
        result = db.execute_query("SELECT * FROM categories WHERE id = ?", (category_id,))
        if result:
            return Category(**result[0])
        return None

@dataclass
class Product:
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    category_id: Optional[int] = None
    sku: str = ""
    price: float = 0.0
    quantity: int = 0
    min_stock_level: int = 10
    supplier_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def save(self) -> bool:
        """Save product to database"""
        if self.id:
            query = """UPDATE products SET name=?, description=?, category_id=?, sku=?, 
                      price=?, quantity=?, min_stock_level=?, supplier_id=?, updated_at=CURRENT_TIMESTAMP 
                      WHERE id=?"""
            params = (self.name, self.description, self.category_id, self.sku, 
                     self.price, self.quantity, self.min_stock_level, self.supplier_id, self.id)
        else:
            query = """INSERT INTO products (name, description, category_id, sku, price, 
                      quantity, min_stock_level, supplier_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"""
            params = (self.name, self.description, self.category_id, self.sku, 
                     self.price, self.quantity, self.min_stock_level, self.supplier_id)
        
        return db.execute_update(query, params)
    
    def delete(self) -> bool:
        """Delete product from database"""
        if self.id:
            return db.execute_update("DELETE FROM products WHERE id=?", (self.id,))
        return False
    
    @staticmethod
    def get_all() -> List['Product']:
        """Get all products"""
        result = db.execute_query("SELECT * FROM products ORDER BY name")
        return [Product(**row) for row in result] if result else []
    
    @staticmethod
    def get_by_id(product_id: int) -> Optional['Product']:
        """Get product by ID"""
        result = db.execute_query("SELECT * FROM products WHERE id = ?", (product_id,))
        if result:
            return Product(**result[0])
        return None
    
    @staticmethod
    def get_low_stock(threshold: int = 10) -> List['Product']:
        """Get products with low stock"""
        result = db.execute_query(
            "SELECT * FROM products WHERE quantity <= ? ORDER BY quantity", 
            (threshold,)
        )
        return [Product(**row) for row in result] if result else []
    
    @staticmethod
    def search(query: str) -> List['Product']:
        """Search products by name or SKU"""
        search_query = f"%{query}%"
        result = db.execute_query(
            "SELECT * FROM products WHERE name LIKE ? OR sku LIKE ? ORDER BY name",
            (search_query, search_query)
        )
        return [Product(**row) for row in result] if result else []

@dataclass
class Transaction:
    id: Optional[int] = None
    product_id: int = 0
    user_id: int = 0
    transaction_type: str = ""  # 'IN', 'OUT', 'ADJUSTMENT'
    quantity: int = 0
    price: Optional[float] = None
    notes: str = ""
    created_at: Optional[datetime] = None
    
    def save(self) -> bool:
        """Save transaction to database"""
        query = """INSERT INTO transactions (product_id, user_id, transaction_type, 
                  quantity, price, notes) VALUES (?, ?, ?, ?, ?, ?)"""
        params = (self.product_id, self.user_id, self.transaction_type, 
                 self.quantity, self.price, self.notes)
        
        return db.execute_update(query, params)
    
    @staticmethod
    def get_by_product(product_id: int) -> List['Transaction']:
        """Get transactions for a specific product"""
        result = db.execute_query(
            "SELECT * FROM transactions WHERE product_id = ? ORDER BY created_at DESC",
            (product_id,)
        )
        return [Transaction(**row) for row in result] if result else []
    
    @staticmethod
    def get_recent(limit: int = 50) -> List['Transaction']:
        """Get recent transactions"""
        result = db.execute_query(
            "SELECT * FROM transactions ORDER BY created_at DESC LIMIT ?",
            (limit,)
        )
        return [Transaction(**row) for row in result] if result else []
