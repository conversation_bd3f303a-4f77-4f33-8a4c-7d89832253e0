{"cells": [{"cell_type": "code", "execution_count": 6, "id": "0b822e4c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}], "source": ["greeting = \"Hello \"\n", "name = \"World\"\n", "\n", "message = greeting + name\n", "print (message)"]}, {"cell_type": "code", "execution_count": null, "id": "2f7d742a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "702ca7fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PythonPythonPython\n"]}], "source": ["word = \"Python\"\n", "times = 3\n", "\n", "repeated_word = word * times\n", "print (repeated_word )"]}, {"cell_type": "code", "execution_count": 9, "id": "318f634e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your name is :usa\n"]}], "source": ["user_name = input(\"Input Name:\")\n", "print(\"Your name is :\"+ user_name)"]}, {"cell_type": "code", "execution_count": 16, "id": "81ffc58d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your age :12\n"]}], "source": ["user_age = str(input(\"Input Your Age: \"))\n", "print (\"Your age :\"+ user_age)"]}, {"cell_type": "code", "execution_count": null, "id": "2e6bdf17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "1\n", "2\n", "2.0\n"]}], "source": ["n1 = int(input(\"Number 1:\"))\n", "n2 = int(input(\"Number 2:\"))\n", "\n", "print (n1 + n2)\n", "print (n1 - n2)\n", "print (n1 * n2)\n", "print (n1 / n2)"]}, {"cell_type": "code", "execution_count": null, "id": "9f98bea2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7\n"]}], "source": ["word = str(input(\"Input Words : \"))\n", "word_length = len(word)\n", "print(word_length)"]}, {"cell_type": "code", "execution_count": 2, "id": "635d68ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["53.6\n"]}], "source": ["Celsius = float (input(\"Input Celsius: \"))\n", "Fahrenheit =  (<PERSON><PERSON><PERSON> * 9/5) + 32\n", "\n", "print(Fahrenheit)"]}, {"cell_type": "code", "execution_count": null, "id": "7ce1e8c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["144.0\n"]}], "source": ["length = float (input(\"Input Length: \"))\n", "width = float (input(\"Input width: \"))\n", "area = length * width\n", "\n", "print(area)"]}, {"cell_type": "code", "execution_count": null, "id": "1fbeada5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ProFun\n", "The first three are: Pro\n", "The Last three are: Fun\n"]}], "source": ["# text = str(input(\"Input Text: \"))\n", "text = \"ProgrammingIsFun\"\n", "first_three = text[:3]\n", "last_three = text[-3:]\n", "\n", "print(first_three + last_three)\n", "print(\"The first three are: \"+first_three)\n", "print(\"The Last three are: \"+last_three)"]}, {"cell_type": "code", "execution_count": 10, "id": "5cc0f276", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON><PERSON> Your favorite number is 20\n"]}], "source": ["name = input(\"Input Name: \")\n", "number = int(input(\"Input Number: \"))\n", "\n", "print(f\"Hello, {name} Your favorite number is {number}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}