class Car:
    turn_left = False
    turn_right = False
    def __init__(self, color: str, model: str):
        self.color = color
        self.model = model
    def turn(self, direction: str):
        if direction.lower() == "left":
            self.turn_left = True
            self.turn_right = False
            print(f"Turning {self.model} left")
        elif direction.lower() == "right":
            self.turn_left = False
            self.turn_right = True
            print(f"Turning {self.model} right")
        else:
            raise ValueError("Invalid direction. Must be 'left' or 'right'")
    