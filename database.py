# Database connection and management for Inventory Management System

import sqlite3
from typing import Optional, Any, Dict, List
import hashlib
from datetime import datetime
from config import CURRENT_DB, get_database_config

# Optional database connectors
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import psycopg2
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.db_type = CURRENT_DB
        self.config = get_database_config()
        
    def connect(self) -> bool:
        """Establish database connection"""
        try:
            if self.db_type == 'sqlite':
                self.connection = sqlite3.connect(self.config['database'])
                self.connection.row_factory = sqlite3.Row
            elif self.db_type == 'mysql':
                if not MYSQL_AVAILABLE:
                    print("MySQL connector not available. Install with: pip install mysql-connector-python")
                    return False
                self.connection = mysql.connector.connect(**self.config)
            elif self.db_type == 'postgresql':
                if not POSTGRESQL_AVAILABLE:
                    print("PostgreSQL connector not available. Install with: pip install psycopg2-binary")
                    return False
                self.connection = psycopg2.connect(**self.config)

            print(f"Connected to {self.db_type} database successfully")
            return True
        except Exception as e:
            print(f"Database connection error: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = None) -> Optional[List[Dict]]:
        """Execute a SELECT query and return results"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if self.db_type == 'sqlite':
                return [dict(row) for row in cursor.fetchall()]
            else:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"Query execution error: {e}")
            return None
    
    def execute_update(self, query: str, params: tuple = None) -> bool:
        """Execute INSERT, UPDATE, or DELETE query"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            self.connection.commit()
            return True
        except Exception as e:
            print(f"Update execution error: {e}")
            self.connection.rollback()
            return False
    
    def create_tables(self) -> bool:
        """Create all necessary tables"""
        tables = {
            'users': '''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    role VARCHAR(20) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            ''',
            'categories': '''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100) UNIQUE NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'products': '''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    category_id INTEGER,
                    sku VARCHAR(50) UNIQUE NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 10,
                    supplier_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id),
                    FOREIGN KEY (supplier_id) REFERENCES users(id)
                )
            ''',
            'transactions': '''
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    transaction_type VARCHAR(20) NOT NULL,
                    quantity INTEGER NOT NULL,
                    price DECIMAL(10,2),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products(id),
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''',
            'reports': '''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    report_type VARCHAR(50) NOT NULL,
                    generated_by INTEGER NOT NULL,
                    report_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (generated_by) REFERENCES users(id)
                )
            '''
        }
        
        try:
            for table_name, query in tables.items():
                if not self.execute_update(query):
                    print(f"Failed to create table: {table_name}")
                    return False
            
            # Create default admin user if not exists
            self.create_default_admin()
            print("All tables created successfully")
            return True
        except Exception as e:
            print(f"Error creating tables: {e}")
            return False
    
    def create_default_admin(self):
        """Create default admin user"""
        admin_exists = self.execute_query(
            "SELECT id FROM users WHERE username = ?", ('admin',)
        )
        
        if not admin_exists:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            self.execute_update(
                """INSERT INTO users (username, password_hash, email, role, full_name) 
                   VALUES (?, ?, ?, ?, ?)""",
                ('admin', password_hash, '<EMAIL>', 'admin', 'System Administrator')
            )
            print("Default admin user created (username: admin, password: admin123)")
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return self.hash_password(password) == password_hash

# Global database instance
db = DatabaseManager()
