pi = 3.14

def area(r):
    return pi * r * r

def circumference(r):
    return 2 * pi * r

def sphere_surface_area(r):
    return 4 * area(r)

def sphere_volume(r):
    return (4/3) * pi * r ** 3

# Arithmetic Operations
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    if b == 0:
        return "Cannot divide by zero"
    return a / b

# Input from user
value = float(input("Enter a number: "))

print("*"*30)
print("\nResults:")
print("Area:", area(value))
print("Circumference:", circumference(value))
print("Sphere Surface Area:", sphere_surface_area(value))
print("Sphere Volume:", sphere_volume(value))
print("Addition (value + value):", add(value, value))
print("Subtraction (value - value):", subtract(value, value))
print("Multiplication (value * value):", multiply(value, value))
print("Division (value / value):", divide(value, value))
