# Verification script for Save button functionality

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service

def main():
    """Main verification function"""
    print("=" * 60)
    print("SAVE BUTTON VERIFICATION")
    print("=" * 60)
    
    # Initialize database
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    print("✅ Database initialized and authenticated")
    
    # Create main window
    root = tk.Tk()
    root.title("Save Button Verification")
    root.geometry("500x400")
    
    # Create header
    header_frame = ttk.Frame(root)
    header_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Label(header_frame, text="Save Button Verification Test", 
             font=('Arial', 16, 'bold')).pack()
    
    ttk.Label(header_frame, text="Click the buttons below to test Save button functionality", 
             font=('Arial', 10)).pack(pady=5)
    
    # Create test results area
    results_frame = ttk.LabelFrame(root, text="Test Results", padding=10)
    results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
    
    results_text = tk.Text(results_frame, height=15, width=60)
    scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
    results_text.configure(yscrollcommand=scrollbar.set)
    
    results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_result(message):
        """Log a result to the text area"""
        results_text.insert(tk.END, message + "\n")
        results_text.see(tk.END)
        root.update()
    
    def test_user_dialog():
        """Test UserDialog Save button"""
        log_result("🔄 Testing UserDialog...")
        try:
            from dialogs import UserDialog
            dialog = UserDialog(root, "Test User Dialog")
            log_result("✅ UserDialog opened successfully")
            log_result("📋 Check if Save and Cancel buttons are visible at the bottom")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                log_result(f"✅ UserDialog Save worked! Result: {dialog.result}")
            else:
                log_result("❌ UserDialog was cancelled or failed")
                
        except Exception as e:
            log_result(f"❌ UserDialog error: {e}")
    
    def test_product_dialog():
        """Test ProductDialog Save button"""
        log_result("🔄 Testing ProductDialog...")
        try:
            from dialogs import ProductDialog
            dialog = ProductDialog(root, "Test Product Dialog")
            log_result("✅ ProductDialog opened successfully")
            log_result("📋 Check if Save and Cancel buttons are visible at the bottom")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                log_result(f"✅ ProductDialog Save worked! Result: {dialog.result}")
            else:
                log_result("❌ ProductDialog was cancelled or failed")
                
        except Exception as e:
            log_result(f"❌ ProductDialog error: {e}")
    
    def test_category_dialog():
        """Test CategoryDialog Save button"""
        log_result("🔄 Testing CategoryDialog...")
        try:
            from dialogs import CategoryDialog
            dialog = CategoryDialog(root, "Test Category Dialog")
            log_result("✅ CategoryDialog opened successfully")
            log_result("📋 Check if Save and Cancel buttons are visible at the bottom")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                log_result(f"✅ CategoryDialog Save worked! Result: {dialog.result}")
            else:
                log_result("❌ CategoryDialog was cancelled or failed")
                
        except Exception as e:
            log_result(f"❌ CategoryDialog error: {e}")
    
    def run_all_tests():
        """Run all dialog tests"""
        log_result("=" * 50)
        log_result("RUNNING ALL DIALOG TESTS")
        log_result("=" * 50)
        log_result("Instructions:")
        log_result("1. Each dialog will open automatically")
        log_result("2. Look for Save and Cancel buttons at the bottom")
        log_result("3. Fill in required fields and click Save to test")
        log_result("4. Or click Cancel to skip")
        log_result("")
        
        # Test each dialog
        test_user_dialog()
        test_product_dialog()
        test_category_dialog()
        
        log_result("=" * 50)
        log_result("ALL TESTS COMPLETED")
        log_result("=" * 50)
    
    # Create test buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Button(button_frame, text="Test User Dialog", 
              command=test_user_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Test Product Dialog", 
              command=test_product_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Test Category Dialog", 
              command=test_category_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(button_frame, text="Run All Tests", 
              command=run_all_tests).pack(side=tk.LEFT, padx=10)
    
    ttk.Button(button_frame, text="Close", 
              command=root.destroy).pack(side=tk.RIGHT, padx=5)
    
    # Initial log
    log_result("✅ Verification system ready")
    log_result("📋 Click test buttons to verify Save button functionality")
    log_result("")
    
    print("✅ Verification window opened")
    print("📋 Use the GUI to test Save button functionality")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()
    
    print("✅ Verification completed")

if __name__ == "__main__":
    main()
