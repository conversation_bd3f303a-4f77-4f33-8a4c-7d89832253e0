# User Management GUI for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox
from user_management import user_service
from models import User
from config import GUI_CONFIG, USER_ROLES
from typing import Optional

class UserManagementFrame:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        
        # Current selected user
        self.selected_user = None
        
        # Create user management interface
        self.create_interface()
        
        # Load initial data
        self.refresh_data()
    
    def create_interface(self):
        """Create the user management interface"""
        # Title and controls
        self.create_header()
        
        # Main content area with notebook
        self.create_notebook()
        
        # User list tab
        self.create_user_list_tab()
        
        # Password management tab
        self.create_password_tab()
    
    def create_header(self):
        """Create header with title and main controls"""
        header_frame = ttk.Frame(self.frame)
        header_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['medium'])
        
        # Title
        ttk.Label(header_frame, text="User Management", 
                 font=GUI_CONFIG['fonts']['title']).pack(side=tk.LEFT)
        
        # Main action buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="Add User", 
                  command=self.add_user).pack(side=tk.LEFT, 
                                             padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(button_frame, text="Refresh", 
                  command=self.refresh_data).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
    
    def create_notebook(self):
        """Create notebook for different tabs"""
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, 
                          padx=GUI_CONFIG['padding']['medium'], 
                          pady=GUI_CONFIG['padding']['small'])
    
    def create_user_list_tab(self):
        """Create user list and management tab"""
        # Create tab frame
        self.user_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.user_tab, text="Users")
        
        # Search frame
        search_frame = ttk.Frame(self.user_tab)
        search_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['small'])
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(search_frame, text="Clear", 
                  command=self.clear_search).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
        
        # User list frame
        list_frame = ttk.Frame(self.user_tab)
        list_frame.pack(fill=tk.BOTH, expand=True, 
                       padx=GUI_CONFIG['padding']['medium'], 
                       pady=GUI_CONFIG['padding']['small'])
        
        # Create treeview for users
        columns = ('ID', 'Username', 'Full Name', 'Email', 'Role', 'Status', 'Created')
        self.user_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Username': 120, 'Full Name': 150, 'Email': 180, 
                        'Role': 120, 'Status': 80, 'Created': 120}
        
        for col in columns:
            self.user_tree.heading(col, text=col)
            self.user_tree.column(col, width=column_widths.get(col, 100))
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.user_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.user_tree.xview)
        self.user_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.user_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind selection event
        self.user_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        self.user_tree.bind('<Double-1>', self.edit_user)
        
        # Action buttons frame
        action_frame = ttk.Frame(self.user_tab)
        action_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="Edit User", 
                  command=self.edit_user).pack(side=tk.LEFT, 
                                              padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="Reset Password", 
                  command=self.reset_password).pack(side=tk.LEFT, 
                                                   padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="Toggle Status", 
                  command=self.toggle_user_status).pack(side=tk.LEFT, 
                                                       padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="View Details", 
                  command=self.view_user_details).pack(side=tk.LEFT, 
                                                      padx=GUI_CONFIG['padding']['small'])
    
    def create_password_tab(self):
        """Create password management tab"""
        self.password_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.password_tab, text="Password Management")
        
        # Change own password frame
        own_password_frame = ttk.LabelFrame(self.password_tab, text="Change Your Password", 
                                           padding=GUI_CONFIG['padding']['medium'])
        own_password_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                               pady=GUI_CONFIG['padding']['medium'])
        
        # Current password
        ttk.Label(own_password_frame, text="Current Password:").grid(row=0, column=0, sticky='w', 
                                                                    pady=GUI_CONFIG['padding']['small'])
        self.current_password_var = tk.StringVar()
        ttk.Entry(own_password_frame, textvariable=self.current_password_var, 
                 show="*", width=30).grid(row=0, column=1, sticky='ew', 
                                         padx=GUI_CONFIG['padding']['small'])
        
        # New password
        ttk.Label(own_password_frame, text="New Password:").grid(row=1, column=0, sticky='w', 
                                                                pady=GUI_CONFIG['padding']['small'])
        self.new_password_var = tk.StringVar()
        ttk.Entry(own_password_frame, textvariable=self.new_password_var, 
                 show="*", width=30).grid(row=1, column=1, sticky='ew', 
                                         padx=GUI_CONFIG['padding']['small'])
        
        # Confirm password
        ttk.Label(own_password_frame, text="Confirm Password:").grid(row=2, column=0, sticky='w', 
                                                                    pady=GUI_CONFIG['padding']['small'])
        self.confirm_password_var = tk.StringVar()
        ttk.Entry(own_password_frame, textvariable=self.confirm_password_var, 
                 show="*", width=30).grid(row=2, column=1, sticky='ew', 
                                         padx=GUI_CONFIG['padding']['small'])
        
        # Change password button
        ttk.Button(own_password_frame, text="Change Password", 
                  command=self.change_own_password).grid(row=3, column=1, 
                                                        pady=GUI_CONFIG['padding']['medium'])
        
        # Configure grid weights
        own_password_frame.columnconfigure(1, weight=1)
        
        # Forgot password frame (for admins to reset any user's password)
        if user_service.has_permission(USER_ROLES['ADMIN']):
            forgot_password_frame = ttk.LabelFrame(self.password_tab, text="Reset User Password (Admin)", 
                                                  padding=GUI_CONFIG['padding']['medium'])
            forgot_password_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                                      pady=GUI_CONFIG['padding']['medium'])
            
            # User selection
            ttk.Label(forgot_password_frame, text="Select User:").grid(row=0, column=0, sticky='w', 
                                                                      pady=GUI_CONFIG['padding']['small'])
            self.reset_user_var = tk.StringVar()
            self.reset_user_combo = ttk.Combobox(forgot_password_frame, textvariable=self.reset_user_var, 
                                                state='readonly', width=40)
            self.reset_user_combo.grid(row=0, column=1, sticky='ew', 
                                      padx=GUI_CONFIG['padding']['small'])
            
            # New password for user
            ttk.Label(forgot_password_frame, text="New Password:").grid(row=1, column=0, sticky='w', 
                                                                       pady=GUI_CONFIG['padding']['small'])
            self.admin_new_password_var = tk.StringVar()
            ttk.Entry(forgot_password_frame, textvariable=self.admin_new_password_var, 
                     show="*", width=40).grid(row=1, column=1, sticky='ew', 
                                             padx=GUI_CONFIG['padding']['small'])
            
            # Generate password button
            ttk.Button(forgot_password_frame, text="Generate Password", 
                      command=self.generate_password).grid(row=1, column=2, 
                                                          padx=GUI_CONFIG['padding']['small'])
            
            # Reset password button
            ttk.Button(forgot_password_frame, text="Reset Password", 
                      command=self.admin_reset_password).grid(row=2, column=1, 
                                                             pady=GUI_CONFIG['padding']['medium'])
            
            # Configure grid weights
            forgot_password_frame.columnconfigure(1, weight=1)
            
            # Load users for password reset
            self.load_users_for_reset()
    
    def refresh_data(self):
        """Refresh all data in the interface"""
        self.load_users()
        if user_service.has_permission(USER_ROLES['ADMIN']):
            self.load_users_for_reset()

    def load_users(self):
        """Load users into the treeview"""
        # Clear existing items
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)

        try:
            users = user_service.get_all_users()

            for user in users:
                # Format role
                role_display = user.role.replace('_', ' ').title()

                # Format status
                status = "Active" if user.is_active else "Inactive"

                # Format created date
                created_date = str(user.created_at)[:10] if user.created_at else "Unknown"

                # Insert into treeview
                self.user_tree.insert('', 'end', values=(
                    user.id,
                    user.username,
                    user.full_name,
                    user.email,
                    role_display,
                    status,
                    created_date
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load users: {str(e)}")

    def load_users_for_reset(self):
        """Load users into password reset combobox"""
        try:
            users = user_service.get_all_users()
            user_list = [f"{user.full_name} ({user.username})" for user in users]
            self.reset_user_combo['values'] = user_list
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load users for password reset: {str(e)}")

    def on_search_change(self, *args):
        """Handle search text change"""
        search_term = self.search_var.get().strip()
        if search_term:
            self.search_users(search_term)
        else:
            self.load_users()

    def search_users(self, query: str):
        """Search and display users"""
        # Clear existing items
        for item in self.user_tree.get_children():
            self.user_tree.delete(item)

        try:
            users = user_service.search_users(query)

            for user in users:
                role_display = user.role.replace('_', ' ').title()
                status = "Active" if user.is_active else "Inactive"
                created_date = str(user.created_at)[:10] if user.created_at else "Unknown"

                self.user_tree.insert('', 'end', values=(
                    user.id,
                    user.username,
                    user.full_name,
                    user.email,
                    role_display,
                    status,
                    created_date
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Search failed: {str(e)}")

    def clear_search(self):
        """Clear search and reload all users"""
        self.search_var.set("")
        self.load_users()

    def on_user_select(self, event):
        """Handle user selection"""
        selection = self.user_tree.selection()
        if selection:
            item = self.user_tree.item(selection[0])
            user_id = item['values'][0]
            self.selected_user = User.get_by_id(user_id)

    def add_user(self):
        """Add a new user"""
        from dialogs import UserDialog
        dialog = UserDialog(self.frame, "Add User")
        if dialog.result:
            try:
                success = user_service.create_user(
                    username=dialog.result['username'],
                    password=dialog.result['password'],
                    email=dialog.result['email'],
                    role=dialog.result['role'],
                    full_name=dialog.result['full_name']
                )

                if success:
                    messagebox.showinfo("Success", "User added successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to add user")
            except ValueError as e:
                messagebox.showerror("Error", str(e))
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def edit_user(self, event=None):
        """Edit selected user"""
        if not self.selected_user:
            messagebox.showwarning("Warning", "Please select a user to edit")
            return

        from dialogs import UserDialog
        dialog = UserDialog(self.frame, "Edit User", self.selected_user)
        if dialog.result:
            try:
                success = user_service.update_user(
                    user_id=self.selected_user.id,
                    username=dialog.result['username'],
                    email=dialog.result['email'],
                    role=dialog.result['role'],
                    full_name=dialog.result['full_name'],
                    is_active=dialog.result['is_active']
                )

                if success:
                    messagebox.showinfo("Success", "User updated successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to update user")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def reset_password(self):
        """Reset password for selected user"""
        if not self.selected_user:
            messagebox.showwarning("Warning", "Please select a user to reset password")
            return

        from dialogs import PasswordResetDialog
        dialog = PasswordResetDialog(self.frame, self.selected_user)
        if dialog.result:
            try:
                success = user_service.change_password(self.selected_user.id, dialog.result['password'])
                if success:
                    messagebox.showinfo("Success", "Password reset successfully!")
                else:
                    messagebox.showerror("Error", "Failed to reset password")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def toggle_user_status(self):
        """Toggle user active/inactive status"""
        if not self.selected_user:
            messagebox.showwarning("Warning", "Please select a user to toggle status")
            return

        current_status = "active" if self.selected_user.is_active else "inactive"
        new_status = "inactive" if self.selected_user.is_active else "active"

        confirm = messagebox.askyesno(
            "Confirm Status Change",
            f"Change user '{self.selected_user.full_name}' from {current_status} to {new_status}?"
        )

        if confirm:
            try:
                if self.selected_user.is_active:
                    success = user_service.deactivate_user(self.selected_user.id)
                else:
                    success = user_service.activate_user(self.selected_user.id)

                if success:
                    messagebox.showinfo("Success", f"User status changed to {new_status}!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to change user status")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def view_user_details(self):
        """View detailed user information"""
        if not self.selected_user:
            messagebox.showwarning("Warning", "Please select a user to view details")
            return

        from dialogs import UserDetailsDialog
        UserDetailsDialog(self.frame, self.selected_user)

    def change_own_password(self):
        """Change current user's password"""
        current_password = self.current_password_var.get()
        new_password = self.new_password_var.get()
        confirm_password = self.confirm_password_var.get()

        # Validate inputs
        if not current_password:
            messagebox.showerror("Error", "Please enter your current password")
            return

        if not new_password:
            messagebox.showerror("Error", "Please enter a new password")
            return

        if len(new_password) < 6:
            messagebox.showerror("Error", "Password must be at least 6 characters long")
            return

        if new_password != confirm_password:
            messagebox.showerror("Error", "New password and confirmation do not match")
            return

        # Verify current password
        current_user = user_service.get_current_user()
        if not user_service.authenticate(current_user.username, current_password):
            messagebox.showerror("Error", "Current password is incorrect")
            return

        try:
            success = user_service.change_password(current_user.id, new_password)
            if success:
                messagebox.showinfo("Success", "Password changed successfully!")
                # Clear form
                self.current_password_var.set("")
                self.new_password_var.set("")
                self.confirm_password_var.set("")
            else:
                messagebox.showerror("Error", "Failed to change password")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def generate_password(self):
        """Generate a random password"""
        import random
        import string

        # Generate a random password with letters, digits, and special characters
        length = 12
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choice(characters) for _ in range(length))

        self.admin_new_password_var.set(password)

        # Show the generated password to admin
        messagebox.showinfo("Generated Password",
                           f"Generated password: {password}\n\nPlease save this password and share it securely with the user.")

    def admin_reset_password(self):
        """Reset password for selected user (admin only)"""
        selected = self.reset_user_var.get()
        new_password = self.admin_new_password_var.get()

        if not selected:
            messagebox.showerror("Error", "Please select a user")
            return

        if not new_password:
            messagebox.showerror("Error", "Please enter a new password")
            return

        if len(new_password) < 6:
            messagebox.showerror("Error", "Password must be at least 6 characters long")
            return

        # Extract username from selection
        username = selected.split('(')[-1].rstrip(')')
        user = User.get_by_username(username)

        if not user:
            messagebox.showerror("Error", "User not found")
            return

        confirm = messagebox.askyesno(
            "Confirm Password Reset",
            f"Reset password for user '{user.full_name}' ({user.username})?"
        )

        if confirm:
            try:
                success = user_service.change_password(user.id, new_password)
                if success:
                    messagebox.showinfo("Success",
                                       f"Password reset successfully for {user.full_name}!\n\nNew password: {new_password}\n\nPlease share this password securely with the user.")
                    # Clear form
                    self.admin_new_password_var.set("")
                    self.reset_user_var.set("")
                else:
                    messagebox.showerror("Error", "Failed to reset password")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def get_frame(self):
        """Get the main frame"""
        return self.frame
