{"cells": [{"cell_type": "code", "execution_count": 12, "id": "9d4c9c64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello python\n", "7\n", "1\n", "12\n", "0.5\n", "2.2727272727272725\n", "2.0\n", "1\n", "0\n", "8\n", "-16\n", "16\n", "1.5\n"]}], "source": ["print('hello python')\n", "# print(\"3 + 4 =\", 3 + 4)\n", "# print(\"4 - 3 =\", 4 - 3)\n", "# print(\"3 * 4 =\", 3 * 4)\n", "# print(\"1 / 2 =\", 1 / 2)\n", "print(3 + 4)\n", "print(4 - 3)\n", "print(3 * 4)\n", "print(1 / 2)\n", "print(5 / 2.2)\n", "print(5 // 2.2)\n", "print(10 % 3)\n", "print(9 % 3)\n", "print(2 ** 3)\n", "print(-2 ** 4)\n", "print((-2) ** 4)\n", "print(1 + 2 * 3 / 4 -1)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "2500799c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What Your name\n", "Hello,USA\n"]}], "source": ["print (\"What Your name\")\n", "\n", "n = input (\"Your Name :\")\n", "print (\"Hello,\" + n)"]}, {"cell_type": "code", "execution_count": 5, "id": "ef950269", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON><PERSON>! My name's     \n", "...:<PERSON>. I won the World    \n", "...:Championship of Python Contest    \n", "last year.\n"]}], "source": ["print(\"Hello, <PERSON><PERSON>! My name's \\\n", "    \\n...:<PERSON> Usa. I won the World\\\n", "    \\n...:Championship of Python Contest\\\n", "    \\nlast year.\")"]}, {"cell_type": "code", "execution_count": 3, "id": "9cbfcec4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter propositional logic expression using variables like P, Q, R and operators:\n", "NOT: ¬ ~ !\n", "AND: ∧ & &&\n", "OR: ∨ | ||\n", "IMPLIES: → ->\n", "BICONDITIONAL: ↔️ <->\n", "Parentheses: ( )\n", "\n", "\n", "Enter truth values for propositions:\n", "\n", "Result: True\n"]}], "source": ["import re\n", "\n", "# Define operator precedence (higher number = higher precedence)\n", "precedence = {\n", "    '!': 5, '~': 5, '¬': 5,\n", "    '&': 4, '∧': 4,\n", "    '|': 3, '∨': 3,\n", "    '→': 2, '->': 2,\n", "    '↔️': 1, '<->': 1\n", "}\n", "\n", "# Normalize input symbols to internal standard\n", "def normalize(expr):\n", "    expr = expr.replace('&&', '&').replace('∧', '&')\n", "    expr = expr.replace('||', '|').replace('∨', '|')\n", "    expr = expr.replace('~', '!').replace('¬', '!')\n", "    expr = expr.replace('->', '→').replace('→', '→')\n", "    expr = expr.replace('<->', '↔️').replace('↔️', '↔️')\n", "    return expr\n", "\n", "# Get propositions (single uppercase letters)\n", "def get_propositions(expr):\n", "    return sorted(set(re.findall(r'\\b[A-Z]\\b', expr)))\n", "\n", "# Shunting Yard: convert infix to postfix (list)\n", "def infix_to_postfix(expr):\n", "    expr = normalize(expr)\n", "    output = []\n", "    stack = []\n", "\n", "    i = 0\n", "    while i < len(expr):\n", "        c = expr[i]\n", "\n", "        if c == ' ':\n", "            i += 1\n", "            continue\n", "\n", "        # Propositions: uppercase letters\n", "        if c.isalpha() and c.isupper():\n", "            output.append(c)\n", "            i += 1\n", "            continue\n", "\n", "        # Parentheses\n", "        if c == '(':\n", "            stack.append(c)\n", "            i += 1\n", "            continue\n", "        if c == ')':\n", "            while stack and stack[-1] != '(':\n", "                output.append(stack.pop())\n", "            stack.pop()  # Remove '('\n", "            i += 1\n", "            continue\n", "\n", "        # Operators (including two-char)\n", "        # Check for two-char operators like '→' and '↔️'\n", "        if i+1 < len(expr) and expr[i:i+2] in ['→', '↔️']:\n", "            op = expr[i:i+2]\n", "            i += 2\n", "        else:\n", "            op = c\n", "            i += 1\n", "\n", "        while stack and stack[-1] != '(' and precedence.get(stack[-1], 0) >= precedence.get(op, 0):\n", "            output.append(stack.pop())\n", "        stack.append(op)\n", "\n", "    while stack:\n", "        output.append(stack.pop())\n", "\n", "    return output\n", "\n", "# Evaluate postfix with truth values dictionary\n", "def evaluate_postfix(postfix, values):\n", "    stack = []\n", "    for token in postfix:\n", "        if token in values:\n", "            stack.append(values[token])\n", "        elif token == '!':\n", "            val = stack.pop()\n", "            stack.append(not val)\n", "        elif token == '&':\n", "            b = stack.pop()\n", "            a = stack.pop()\n", "            stack.append(a and b)\n", "        elif token == '|':\n", "            b = stack.pop()\n", "            a = stack.pop()\n", "            stack.append(a or b)\n", "        elif token == '→':\n", "            b = stack.pop()\n", "            a = stack.pop()\n", "            stack.append((not a) or b)\n", "        elif token == '↔️':\n", "            b = stack.pop()\n", "            a = stack.pop()\n", "            stack.append(a == b)\n", "        else:\n", "            raise ValueError(f\"Unknown token: {token}\")\n", "    return stack[0]\n", "\n", "def main():\n", "    print(\"Enter propositional logic expression using variables like P, Q, R and operators:\")\n", "    print(\"NOT: ¬ ~ !\")\n", "    print(\"AND: ∧ & &&\")\n", "    print(\"OR: ∨ | ||\")\n", "    print(\"IMPLIES: → ->\")\n", "    print(\"BICONDITIONAL: ↔️ <->\")\n", "    print(\"Parentheses: ( )\\n\")\n", "\n", "    expr = input(\"Expression: \").strip()\n", "    expr = normalize(expr)\n", "\n", "    props = get_propositions(expr)\n", "    if not props:\n", "        print(\"No valid propositions found (A-Z).\")\n", "        return\n", "\n", "    values = {}\n", "    print(\"\\nEnter truth values for propositions:\")\n", "    for p in props:\n", "        while True:\n", "            val = input(f\"  {p} (True/False): \").strip().lower()\n", "            if val in ['true', 't', '1']:\n", "                values[p] = True\n", "                break\n", "            elif val in ['false', 'f', '0']:\n", "                values[p] = False\n", "                break\n", "            else:\n", "                print(\"Please enter True or False.\")\n", "\n", "    postfix = infix_to_postfix(expr)\n", "    result = evaluate_postfix(postfix, values)\n", "\n", "    print(f\"\\nResult: {result}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}