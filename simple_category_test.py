# Simple category dialog test without custom create_buttons

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service
from config import GUI_CONFIG, USER_ROLES
from typing import Optional, Dict, Any

class SimpleCategoryDialog:
    """Simplified CategoryDialog for testing"""
    def __init__(self, parent, title: str):
        self.result = None
        self.parent = parent
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        # Create main frame
        self.main_frame = ttk.Frame(self.dialog, padding=GUI_CONFIG['padding']['medium'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create content
        self.create_content()
        
        # Create buttons
        self.create_buttons()
        
        # Focus on dialog
        self.dialog.focus_set()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_rootx() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_rooty() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_content(self):
        """Create category form content"""
        # Category name
        ttk.Label(self.main_frame, text="Category Name:*").pack(anchor='w', pady=(0, 5))
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(self.main_frame, textvariable=self.name_var, width=40)
        self.name_entry.pack(fill='x', pady=(0, 10))
        self.name_entry.focus()
        
        # Description
        ttk.Label(self.main_frame, text="Description:").pack(anchor='w', pady=(0, 5))
        self.description_text = tk.Text(self.main_frame, width=40, height=5)
        self.description_text.pack(fill='both', expand=True, pady=(0, 10))
        
        # Required fields note
        ttk.Label(self.main_frame, text="* Required fields", 
                 font=('Arial', 8), foreground='gray').pack(anchor='w')
    
    def create_buttons(self):
        """Create Save and Cancel buttons"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Create buttons with explicit commands
        cancel_btn = ttk.Button(button_frame, text="Cancel", command=self.cancel)
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        save_btn = ttk.Button(button_frame, text="Save", command=self.save)
        save_btn.pack(side=tk.RIGHT)
        
        print("✅ Buttons created with commands:")
        print(f"   Save button command: {save_btn['command']}")
        print(f"   Cancel button command: {cancel_btn['command']}")
    
    def save(self):
        """Handle Save button"""
        print("🔄 Save button clicked!")
        
        try:
            # Get form data
            name = self.name_var.get().strip()
            description = self.description_text.get('1.0', 'end-1c').strip()
            
            print(f"📝 Form data captured:")
            print(f"   Name: '{name}'")
            print(f"   Description: '{description}'")
            
            # Validate
            if not name:
                print("❌ Validation failed: empty name")
                messagebox.showerror("Error", "Category name is required")
                return
            
            print("✅ Validation passed")
            
            # Set result
            self.result = {
                'name': name,
                'description': description
            }
            
            print(f"✅ Result set: {self.result}")
            
            # Close dialog
            print("🔄 Closing dialog...")
            self.dialog.destroy()
            print("✅ Dialog closed")
            
        except Exception as e:
            print(f"❌ Error in save method: {e}")
            import traceback
            traceback.print_exc()
    
    def cancel(self):
        """Handle Cancel button"""
        print("❌ Cancel button clicked")
        self.result = None
        self.dialog.destroy()

def test_simple_category_dialog():
    """Test the simplified category dialog"""
    print("=" * 60)
    print("TESTING SIMPLE CATEGORY DIALOG")
    print("=" * 60)
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    print("✅ Database initialized and authenticated")
    
    # Create main window
    root = tk.Tk()
    root.title("Simple Category Dialog Test")
    root.geometry("500x300")
    
    # Instructions
    instructions = tk.Text(root, height=10, width=60)
    instructions.pack(pady=10)
    instructions.insert('1.0', """SIMPLE CATEGORY DIALOG TEST

This test uses a simplified CategoryDialog to isolate the data capture issue.

Instructions:
1. Click 'Test Simple Category Dialog' button below
2. Enter category name: 'Simple Test Category'
3. Enter description: 'This is a simple test'
4. Click Save button
5. Watch console output for detailed debug messages
6. Check result display below

Expected behavior:
- Save button should capture form data
- Console should show debug messages
- Result should be displayed below
""")
    instructions.config(state='disabled')
    
    # Result display
    result_label = ttk.Label(root, text="Result will appear here", 
                            font=('Arial', 12), foreground='blue')
    result_label.pack(pady=10)
    
    def test_dialog():
        """Test the simple dialog"""
        print("\n" + "="*50)
        print("OPENING SIMPLE CATEGORY DIALOG")
        print("="*50)
        
        try:
            dialog = SimpleCategoryDialog(root, "Simple Category Test")
            print("✅ Simple dialog created")
            
            # Wait for dialog to close
            print("⏳ Waiting for user interaction...")
            root.wait_window(dialog.dialog)
            print("✅ Dialog interaction completed")
            
            # Check result
            if dialog.result:
                print(f"✅ SUCCESS! Result captured: {dialog.result}")
                result_text = f"SUCCESS!\nName: {dialog.result['name']}\nDescription: {dialog.result['description']}"
                result_label.config(text=result_text, foreground='green')
                
                # Try to save to database
                try:
                    from product_management import product_service
                    success = product_service.create_category(
                        name=dialog.result['name'],
                        description=dialog.result['description']
                    )
                    if success:
                        print("✅ Category saved to database!")
                        result_label.config(text=result_text + "\n\n✅ SAVED TO DATABASE!", 
                                          foreground='green')
                    else:
                        print("❌ Failed to save to database")
                        result_label.config(text=result_text + "\n\n❌ Database save failed", 
                                          foreground='orange')
                except Exception as e:
                    print(f"❌ Database error: {e}")
                    result_label.config(text=result_text + f"\n\n❌ Database error: {e}", 
                                      foreground='orange')
            else:
                print("❌ No result captured")
                result_label.config(text="❌ No data captured", foreground='red')
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            result_label.config(text=f"❌ Error: {e}", foreground='red')
        
        print("="*50)
        print("SIMPLE DIALOG TEST COMPLETED")
        print("="*50)
    
    # Test button
    ttk.Button(root, text="Test Simple Category Dialog", 
              command=test_dialog).pack(pady=10)
    
    ttk.Button(root, text="Close", 
              command=root.destroy).pack(pady=5)
    
    print("✅ Simple test window opened")
    print("📋 Click 'Test Simple Category Dialog' to start")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()

if __name__ == "__main__":
    test_simple_category_dialog()
