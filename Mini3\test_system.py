# Test script for Inventory Management System

from database import db
from user_management import user_service
from product_management import product_service
from report_management import report_service
from models import User, Product, Category

def test_database_connection():
    """Test database connection and table creation"""
    print("Testing database connection...")
    
    if not db.connect():
        print("❌ Database connection failed")
        return False
    
    if not db.create_tables():
        print("❌ Table creation failed")
        return False
    
    print("✅ Database connection and table creation successful")
    return True

def test_user_management():
    """Test user management functionality"""
    print("\nTesting user management...")
    
    # Test authentication with default admin
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Admin authentication failed")
        return False
    
    print("✅ Admin authentication successful")
    
    # Test creating a new user
    try:
        success = user_service.create_user(
            username='testuser',
            password='testpass',
            email='<EMAIL>',
            role='inventory_manager',
            full_name='Test User'
        )
        
        if success:
            print("✅ User creation successful")
        else:
            print("❌ User creation failed")
            return False
    except Exception as e:
        print(f"❌ User creation error: {e}")
        return False
    
    # Test getting all users
    users = user_service.get_all_users()
    if len(users) >= 2:  # admin + testuser
        print(f"✅ Retrieved {len(users)} users")
    else:
        print("❌ Failed to retrieve users")
        return False
    
    return True

def test_product_management():
    """Test product management functionality"""
    print("\nTesting product management...")
    
    # Create a test category
    try:
        success = product_service.create_category(
            name='Test Category',
            description='A test category'
        )
        
        if success:
            print("✅ Category creation successful")
        else:
            print("❌ Category creation failed")
            return False
    except Exception as e:
        print(f"❌ Category creation error: {e}")
        return False
    
    # Get categories
    categories = product_service.get_all_categories()
    if not categories:
        print("❌ No categories found")
        return False
    
    category_id = categories[0].id
    print(f"✅ Retrieved {len(categories)} categories")
    
    # Create a test product
    try:
        success = product_service.create_product(
            name='Test Product',
            description='A test product',
            category_id=category_id,
            sku='TEST001',
            price=19.99,
            quantity=100,
            min_stock_level=10
        )
        
        if success:
            print("✅ Product creation successful")
        else:
            print("❌ Product creation failed")
            return False
    except Exception as e:
        print(f"❌ Product creation error: {e}")
        return False
    
    # Get products
    products = product_service.get_all_products()
    if not products:
        print("❌ No products found")
        return False
    
    print(f"✅ Retrieved {len(products)} products")
    
    # Test stock adjustment
    product_id = products[0].id
    try:
        success = product_service.stock_out(product_id, 20, notes='Test sale')
        if success:
            print("✅ Stock adjustment successful")
        else:
            print("❌ Stock adjustment failed")
            return False
    except Exception as e:
        print(f"❌ Stock adjustment error: {e}")
        return False
    
    return True

def test_reporting():
    """Test reporting functionality"""
    print("\nTesting reporting...")
    
    try:
        # Generate low stock report
        low_stock_report = report_service.generate_low_stock_report()
        print(f"✅ Low stock report generated with {low_stock_report['total_products']} products")
        
        # Generate inventory report
        inventory_report = report_service.generate_inventory_report()
        print(f"✅ Inventory report generated with {inventory_report['total_products']} products")
        
        # Generate sales report
        sales_report = report_service.generate_sales_report()
        print(f"✅ Sales report generated with {sales_report['summary']['total_transactions']} transactions")
        
        return True
    except Exception as e:
        print(f"❌ Reporting error: {e}")
        return False

def test_statistics():
    """Test statistics functionality"""
    print("\nTesting statistics...")
    
    try:
        # Get product statistics
        product_stats = product_service.get_product_stats()
        print(f"✅ Product statistics: {product_stats}")
        
        # Get user statistics
        user_stats = user_service.get_user_stats()
        print(f"✅ User statistics: {user_stats}")
        
        return True
    except Exception as e:
        print(f"❌ Statistics error: {e}")
        return False

def run_all_tests():
    """Run all system tests"""
    print("=" * 60)
    print("INVENTORY MANAGEMENT SYSTEM - FUNCTIONALITY TEST")
    print("=" * 60)
    
    tests = [
        test_database_connection,
        test_user_management,
        test_product_management,
        test_reporting,
        test_statistics
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! System is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    # Clean up
    if db.connection:
        db.disconnect()
    
    return passed == total

if __name__ == "__main__":
    run_all_tests()
