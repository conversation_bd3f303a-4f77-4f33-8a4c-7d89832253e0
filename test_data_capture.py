# Test script to debug data capture in dialogs

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service

def test_data_capture():
    """Test data capture in dialogs"""
    print("=" * 60)
    print("TESTING DATA CAPTURE IN DIALOGS")
    print("=" * 60)
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    print("✅ Database initialized and authenticated")
    
    # Create main window
    root = tk.Tk()
    root.title("Data Capture Test")
    root.geometry("600x500")
    
    # Create results display
    results_frame = ttk.LabelFrame(root, text="Test Results", padding=10)
    results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    results_text = tk.Text(results_frame, height=20, width=70, font=('Courier', 10))
    scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
    results_text.configure(yscrollcommand=scrollbar.set)
    
    results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_result(message):
        """Log a result to the text area"""
        results_text.insert(tk.END, message + "\n")
        results_text.see(tk.END)
        root.update()
    
    def test_category_dialog():
        """Test CategoryDialog data capture"""
        log_result("🔄 Testing CategoryDialog data capture...")
        try:
            from dialogs import CategoryDialog
            dialog = CategoryDialog(root, "Test Category Dialog")
            
            log_result("✅ CategoryDialog opened")
            log_result("📋 Instructions:")
            log_result("1. Enter category name: 'Test Category'")
            log_result("2. Enter description: 'This is a test category'")
            log_result("3. Click Save button")
            log_result("4. Check if data is captured below")
            log_result("")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                log_result("✅ DATA CAPTURED SUCCESSFULLY!")
                log_result(f"   Name: '{dialog.result.get('name', 'NOT CAPTURED')}'")
                log_result(f"   Description: '{dialog.result.get('description', 'NOT CAPTURED')}'")
                log_result(f"   Full result: {dialog.result}")
            else:
                log_result("❌ NO DATA CAPTURED!")
                log_result("   Dialog result is None or empty")
                if hasattr(dialog, 'result'):
                    log_result(f"   Dialog.result = {dialog.result}")
                else:
                    log_result("   Dialog has no result attribute")
            
            log_result("-" * 50)
            
        except Exception as e:
            log_result(f"❌ CategoryDialog error: {e}")
            import traceback
            log_result(traceback.format_exc())
    
    def test_user_dialog():
        """Test UserDialog data capture"""
        log_result("🔄 Testing UserDialog data capture...")
        try:
            from dialogs import UserDialog
            dialog = UserDialog(root, "Test User Dialog")
            
            log_result("✅ UserDialog opened")
            log_result("📋 Instructions:")
            log_result("1. Enter username: 'testuser'")
            log_result("2. Enter full name: 'Test User'")
            log_result("3. Enter email: '<EMAIL>'")
            log_result("4. Select role: 'Supplier'")
            log_result("5. Enter password: 'password123'")
            log_result("6. Confirm password: 'password123'")
            log_result("7. Click Save button")
            log_result("8. Check if data is captured below")
            log_result("")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if hasattr(dialog, 'result') and dialog.result:
                log_result("✅ DATA CAPTURED SUCCESSFULLY!")
                log_result(f"   Username: '{dialog.result.get('username', 'NOT CAPTURED')}'")
                log_result(f"   Full Name: '{dialog.result.get('full_name', 'NOT CAPTURED')}'")
                log_result(f"   Email: '{dialog.result.get('email', 'NOT CAPTURED')}'")
                log_result(f"   Role: '{dialog.result.get('role', 'NOT CAPTURED')}'")
                log_result(f"   Password: {'SET' if dialog.result.get('password') else 'NOT CAPTURED'}")
                log_result(f"   Full result: {dialog.result}")
            else:
                log_result("❌ NO DATA CAPTURED!")
                log_result("   Dialog result is None or empty")
                if hasattr(dialog, 'result'):
                    log_result(f"   Dialog.result = {dialog.result}")
                else:
                    log_result("   Dialog has no result attribute")
            
            log_result("-" * 50)
            
        except Exception as e:
            log_result(f"❌ UserDialog error: {e}")
            import traceback
            log_result(traceback.format_exc())
    
    def test_simple_dialog():
        """Test a simple custom dialog to verify basic functionality"""
        log_result("🔄 Testing simple custom dialog...")
        
        class SimpleTestDialog:
            def __init__(self, parent):
                self.result = None
                
                # Create dialog window
                self.dialog = tk.Toplevel(parent)
                self.dialog.title("Simple Test Dialog")
                self.dialog.geometry("400x250")
                self.dialog.transient(parent)
                self.dialog.grab_set()
                
                # Create main frame
                main_frame = ttk.Frame(self.dialog, padding=20)
                main_frame.pack(fill=tk.BOTH, expand=True)
                
                # Add form fields
                ttk.Label(main_frame, text="Test Name:").grid(row=0, column=0, sticky='w', pady=5)
                self.name_var = tk.StringVar()
                ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, pady=5)
                
                ttk.Label(main_frame, text="Test Description:").grid(row=1, column=0, sticky='nw', pady=5)
                self.desc_text = tk.Text(main_frame, width=30, height=4)
                self.desc_text.grid(row=1, column=1, pady=5)
                
                # Create buttons
                button_frame = ttk.Frame(main_frame)
                button_frame.grid(row=2, column=0, columnspan=2, pady=20)
                
                ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
                ttk.Button(button_frame, text="Save", command=self.save).pack(side=tk.RIGHT)
            
            def save(self):
                """Handle Save button"""
                name = self.name_var.get().strip()
                description = self.desc_text.get('1.0', tk.END).strip()
                
                if not name:
                    messagebox.showerror("Error", "Name is required")
                    return
                
                self.result = {
                    'name': name,
                    'description': description
                }
                self.dialog.destroy()
            
            def cancel(self):
                """Handle Cancel button"""
                self.result = None
                self.dialog.destroy()
        
        try:
            dialog = SimpleTestDialog(root)
            
            log_result("✅ Simple dialog opened")
            log_result("📋 Instructions:")
            log_result("1. Enter name: 'Simple Test'")
            log_result("2. Enter description: 'This is a simple test'")
            log_result("3. Click Save button")
            log_result("")
            
            # Wait for dialog to close
            root.wait_window(dialog.dialog)
            
            if dialog.result:
                log_result("✅ SIMPLE DIALOG DATA CAPTURED!")
                log_result(f"   Name: '{dialog.result.get('name', 'NOT CAPTURED')}'")
                log_result(f"   Description: '{dialog.result.get('description', 'NOT CAPTURED')}'")
            else:
                log_result("❌ Simple dialog data not captured")
            
            log_result("-" * 50)
            
        except Exception as e:
            log_result(f"❌ Simple dialog error: {e}")
    
    # Create test buttons
    button_frame = ttk.Frame(root)
    button_frame.pack(fill=tk.X, padx=20, pady=10)
    
    ttk.Label(button_frame, text="Click buttons to test data capture:", 
             font=('Arial', 12, 'bold')).pack(pady=5)
    
    test_buttons = ttk.Frame(button_frame)
    test_buttons.pack(pady=5)
    
    ttk.Button(test_buttons, text="Test Category Dialog", 
              command=test_category_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(test_buttons, text="Test User Dialog", 
              command=test_user_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(test_buttons, text="Test Simple Dialog", 
              command=test_simple_dialog).pack(side=tk.LEFT, padx=5)
    
    ttk.Button(test_buttons, text="Close", 
              command=root.destroy).pack(side=tk.LEFT, padx=10)
    
    # Initial instructions
    log_result("🧪 DATA CAPTURE TEST READY")
    log_result("=" * 50)
    log_result("This test will help identify why input data is not being captured.")
    log_result("")
    log_result("Instructions:")
    log_result("1. Click a test button to open a dialog")
    log_result("2. Fill in the form fields as instructed")
    log_result("3. Click Save button")
    log_result("4. Check the results below")
    log_result("")
    log_result("Expected: Data should be captured and displayed")
    log_result("If not captured: There's an issue with the dialog logic")
    log_result("")
    
    print("✅ Data capture test window opened")
    print("📋 Use the GUI to test data capture functionality")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()

if __name__ == "__main__":
    test_data_capture()
