{"cells": [{"cell_type": "code", "execution_count": 9, "id": "7320bcfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 'a', 'b', 'c']\n"]}], "source": ["list1 = [1,2,3,4]\n", "list2 = ['a','b','c']\n", "\n", "list1 + list2\n", "list1 * 3\n", "list2 * 3\n", "\n", "print( list1 + list2)\n"]}, {"cell_type": "code", "execution_count": null, "id": "85b22fdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Welcome to homePage\n"]}], "source": ["users = ['Usa', '123']\n", "\n", "username = str(input('Input Username: '))\n", "password = str(input('Input Password: '))\n", "\n", "if username == users[0] :\n", "    if password == users[1]:\n", "        print('Welcome to homePage')\n", "    else :\n", "        print('Incorrect Password')\n", "else :\n", "    print(\"Please Try Again\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f1c9d9f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-2, 2, 3]\n"]}], "source": ["# copy item\n", "ls1 = [1,2,3]\n", "\n", "ls2 = ls1\n", "ls2[0] = -1\n", "\n", "\n", "print(ls2)"]}, {"cell_type": "code", "execution_count": null, "id": "01f510f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n"]}], "source": ["# count\n", "lst = ['a', 'a', 'b']\n", "\n", "a = lst.count('a')\n", "print(a)"]}, {"cell_type": "code", "execution_count": null, "id": "4e4bb67b", "metadata": {}, "outputs": [], "source": ["lst =  'this is the one of the best'"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}