# User Management Service for Inventory Management System

from typing import Optional, List, Dict, Any
from models import User
from database import db
from config import USER_ROLES
import hashlib

class UserManagementService:
    def __init__(self):
        self.current_user: Optional[User] = None
    
    def authenticate(self, username: str, password: str) -> bool:
        """Authenticate user with username and password"""
        user = User.get_by_username(username)
        if user and user.is_active:
            password_hash = db.hash_password(password)
            if db.verify_password(password, user.password_hash):
                self.current_user = user
                return True
        return False
    
    def logout(self):
        """Logout current user"""
        self.current_user = None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.current_user is not None
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user"""
        return self.current_user
    
    def has_permission(self, required_role: str) -> bool:
        """Check if current user has required permission"""
        if not self.current_user:
            return False
        
        # Admin has all permissions
        if self.current_user.role == USER_ROLES['ADMIN']:
            return True
        
        # Check specific role permissions
        return self.current_user.role == required_role
    
    def create_user(self, username: str, password: str, email: str, 
                   role: str, full_name: str) -> bool:
        """Create a new user"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return False
        
        # Check if username or email already exists
        if User.get_by_username(username):
            raise ValueError("Username already exists")
        
        existing_email = db.execute_query("SELECT id FROM users WHERE email = ?", (email,))
        if existing_email:
            raise ValueError("Email already exists")
        
        # Validate role
        if role not in USER_ROLES.values():
            raise ValueError("Invalid role")
        
        # Create user
        password_hash = db.hash_password(password)
        user = User(
            username=username,
            password_hash=password_hash,
            email=email,
            role=role,
            full_name=full_name,
            is_active=True
        )
        
        return user.save()
    
    def update_user(self, user_id: int, username: str = None, email: str = None,
                   role: str = None, full_name: str = None, is_active: bool = None) -> bool:
        """Update user information"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return False
        
        user = User.get_by_id(user_id)
        if not user:
            return False
        
        # Update fields if provided
        if username:
            # Check if new username already exists (excluding current user)
            existing = db.execute_query(
                "SELECT id FROM users WHERE username = ? AND id != ?", 
                (username, user_id)
            )
            if existing:
                raise ValueError("Username already exists")
            user.username = username
        
        if email:
            # Check if new email already exists (excluding current user)
            existing = db.execute_query(
                "SELECT id FROM users WHERE email = ? AND id != ?", 
                (email, user_id)
            )
            if existing:
                raise ValueError("Email already exists")
            user.email = email
        
        if role and role in USER_ROLES.values():
            user.role = role
        
        if full_name:
            user.full_name = full_name
        
        if is_active is not None:
            user.is_active = is_active
        
        return user.save()
    
    def change_password(self, user_id: int, new_password: str) -> bool:
        """Change user password"""
        # Users can change their own password, admins can change any password
        if not (self.current_user.id == user_id or 
                self.has_permission(USER_ROLES['ADMIN'])):
            return False
        
        password_hash = db.hash_password(new_password)
        return db.execute_update(
            "UPDATE users SET password_hash = ? WHERE id = ?",
            (password_hash, user_id)
        )
    
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return False
        
        return db.execute_update(
            "UPDATE users SET is_active = FALSE WHERE id = ?",
            (user_id,)
        )
    
    def activate_user(self, user_id: int) -> bool:
        """Activate a user account"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return False
        
        return db.execute_update(
            "UPDATE users SET is_active = TRUE WHERE id = ?",
            (user_id,)
        )
    
    def get_all_users(self) -> List[User]:
        """Get all users (admin only)"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return []
        
        return User.get_all()
    
    def get_users_by_role(self, role: str) -> List[User]:
        """Get users by role"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return []
        
        result = db.execute_query("SELECT * FROM users WHERE role = ? ORDER BY username", (role,))
        return [User(**row) for row in result] if result else []
    
    def get_suppliers(self) -> List[User]:
        """Get all supplier users"""
        return self.get_users_by_role(USER_ROLES['SUPPLIER'])
    
    def get_inventory_managers(self) -> List[User]:
        """Get all inventory manager users"""
        return self.get_users_by_role(USER_ROLES['INVENTORY_MANAGER'])
    
    def search_users(self, query: str) -> List[User]:
        """Search users by username, email, or full name"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return []
        
        search_query = f"%{query}%"
        result = db.execute_query(
            """SELECT * FROM users WHERE username LIKE ? OR email LIKE ? 
               OR full_name LIKE ? ORDER BY username""",
            (search_query, search_query, search_query)
        )
        return [User(**row) for row in result] if result else []
    
    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics"""
        if not self.has_permission(USER_ROLES['ADMIN']):
            return {}
        
        stats = {}
        
        # Total users
        total_result = db.execute_query("SELECT COUNT(*) as count FROM users")
        stats['total_users'] = total_result[0]['count'] if total_result else 0
        
        # Active users
        active_result = db.execute_query("SELECT COUNT(*) as count FROM users WHERE is_active = TRUE")
        stats['active_users'] = active_result[0]['count'] if active_result else 0
        
        # Users by role
        for role_name, role_value in USER_ROLES.items():
            role_result = db.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE role = ?", 
                (role_value,)
            )
            stats[f'{role_name.lower()}_count'] = role_result[0]['count'] if role_result else 0
        
        return stats

# Global user management service instance
user_service = UserManagementService()
