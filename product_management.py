# Product Management Service for Inventory Management System

from typing import Optional, List, Dict, Any, Tuple
from models import Product, Category, Transaction
from database import db
from user_management import user_service
from config import USER_ROLES, STOCK_THRESHOLDS

class ProductManagementService:
    def __init__(self):
        pass
    
    def create_product(self, name: str, description: str, category_id: int,
                      sku: str, price: float, quantity: int = 0, 
                      min_stock_level: int = 10, supplier_id: int = None) -> bool:
        """Create a new product"""
        if not user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            return False
        
        # Check if SKU already exists
        existing = db.execute_query("SELECT id FROM products WHERE sku = ?", (sku,))
        if existing:
            raise ValueError("SKU already exists")
        
        product = Product(
            name=name,
            description=description,
            category_id=category_id,
            sku=sku,
            price=price,
            quantity=quantity,
            min_stock_level=min_stock_level,
            supplier_id=supplier_id
        )
        
        success = product.save()
        
        # Log initial stock if quantity > 0
        if success and quantity > 0:
            self.log_transaction(
                product_id=self._get_product_id_by_sku(sku),
                transaction_type='IN',
                quantity=quantity,
                price=price,
                notes='Initial stock'
            )
        
        return success
    
    def update_product(self, product_id: int, name: str = None, description: str = None,
                      category_id: int = None, price: float = None, 
                      min_stock_level: int = None, supplier_id: int = None) -> bool:
        """Update product information"""
        if not user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            return False
        
        product = Product.get_by_id(product_id)
        if not product:
            return False
        
        # Update fields if provided
        if name:
            product.name = name
        if description is not None:
            product.description = description
        if category_id:
            product.category_id = category_id
        if price is not None:
            product.price = price
        if min_stock_level is not None:
            product.min_stock_level = min_stock_level
        if supplier_id is not None:
            product.supplier_id = supplier_id
        
        return product.save()
    
    def delete_product(self, product_id: int) -> bool:
        """Delete a product"""
        if not user_service.has_permission(USER_ROLES['ADMIN']):
            return False
        
        product = Product.get_by_id(product_id)
        if not product:
            return False
        
        return product.delete()
    
    def adjust_stock(self, product_id: int, quantity_change: int, 
                    notes: str = "", price: float = None) -> bool:
        """Adjust product stock (positive for increase, negative for decrease)"""
        if not user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            return False
        
        product = Product.get_by_id(product_id)
        if not product:
            return False
        
        new_quantity = product.quantity + quantity_change
        if new_quantity < 0:
            raise ValueError("Insufficient stock")
        
        # Update product quantity
        product.quantity = new_quantity
        success = product.save()
        
        # Log transaction
        if success:
            transaction_type = 'IN' if quantity_change > 0 else 'OUT'
            self.log_transaction(
                product_id=product_id,
                transaction_type=transaction_type,
                quantity=abs(quantity_change),
                price=price,
                notes=notes
            )
        
        return success
    
    def stock_in(self, product_id: int, quantity: int, price: float = None, 
                notes: str = "") -> bool:
        """Add stock to product"""
        return self.adjust_stock(product_id, quantity, notes, price)
    
    def stock_out(self, product_id: int, quantity: int, price: float = None,
                 notes: str = "") -> bool:
        """Remove stock from product"""
        return self.adjust_stock(product_id, -quantity, notes, price)
    
    def log_transaction(self, product_id: int, transaction_type: str, 
                       quantity: int, price: float = None, notes: str = "") -> bool:
        """Log a stock transaction"""
        if not user_service.current_user:
            return False
        
        transaction = Transaction(
            product_id=product_id,
            user_id=user_service.current_user.id,
            transaction_type=transaction_type,
            quantity=quantity,
            price=price,
            notes=notes
        )
        
        return transaction.save()
    
    def get_all_products(self) -> List[Product]:
        """Get all products"""
        return Product.get_all()
    
    def get_product_by_id(self, product_id: int) -> Optional[Product]:
        """Get product by ID"""
        return Product.get_by_id(product_id)
    
    def search_products(self, query: str) -> List[Product]:
        """Search products by name or SKU"""
        return Product.search(query)
    
    def get_products_by_category(self, category_id: int) -> List[Product]:
        """Get products by category"""
        result = db.execute_query(
            "SELECT * FROM products WHERE category_id = ? ORDER BY name",
            (category_id,)
        )
        return [Product(**row) for row in result] if result else []
    
    def get_low_stock_products(self, threshold: int = None) -> List[Product]:
        """Get products with low stock"""
        if threshold is None:
            threshold = STOCK_THRESHOLDS['LOW_STOCK']
        return Product.get_low_stock(threshold)
    
    def get_out_of_stock_products(self) -> List[Product]:
        """Get products that are out of stock"""
        return Product.get_low_stock(STOCK_THRESHOLDS['OUT_OF_STOCK'])
    
    def get_product_transactions(self, product_id: int) -> List[Transaction]:
        """Get transaction history for a product"""
        return Transaction.get_by_product(product_id)
    
    def _get_product_id_by_sku(self, sku: str) -> Optional[int]:
        """Helper method to get product ID by SKU"""
        result = db.execute_query("SELECT id FROM products WHERE sku = ?", (sku,))
        return result[0]['id'] if result else None
    
    # Category Management Methods
    def create_category(self, name: str, description: str = "") -> bool:
        """Create a new category"""
        if not user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            return False
        
        # Check if category name already exists
        existing = db.execute_query("SELECT id FROM categories WHERE name = ?", (name,))
        if existing:
            raise ValueError("Category name already exists")
        
        category = Category(name=name, description=description)
        return category.save()
    
    def update_category(self, category_id: int, name: str = None, 
                       description: str = None) -> bool:
        """Update category information"""
        if not user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            return False
        
        category = Category.get_by_id(category_id)
        if not category:
            return False
        
        if name:
            # Check if new name already exists (excluding current category)
            existing = db.execute_query(
                "SELECT id FROM categories WHERE name = ? AND id != ?",
                (name, category_id)
            )
            if existing:
                raise ValueError("Category name already exists")
            category.name = name
        
        if description is not None:
            category.description = description
        
        return category.save()
    
    def delete_category(self, category_id: int) -> bool:
        """Delete a category (only if no products are assigned to it)"""
        if not user_service.has_permission(USER_ROLES['ADMIN']):
            return False
        
        # Check if category has products
        products = self.get_products_by_category(category_id)
        if products:
            raise ValueError("Cannot delete category with assigned products")
        
        return db.execute_update("DELETE FROM categories WHERE id = ?", (category_id,))
    
    def get_all_categories(self) -> List[Category]:
        """Get all categories"""
        return Category.get_all()
    
    def get_category_by_id(self, category_id: int) -> Optional[Category]:
        """Get category by ID"""
        return Category.get_by_id(category_id)
    
    def get_product_stats(self) -> Dict[str, Any]:
        """Get product statistics"""
        stats = {}
        
        # Total products
        total_result = db.execute_query("SELECT COUNT(*) as count FROM products")
        stats['total_products'] = total_result[0]['count'] if total_result else 0
        
        # Total categories
        cat_result = db.execute_query("SELECT COUNT(*) as count FROM categories")
        stats['total_categories'] = cat_result[0]['count'] if cat_result else 0
        
        # Low stock products
        low_stock = self.get_low_stock_products()
        stats['low_stock_count'] = len(low_stock)
        
        # Out of stock products
        out_of_stock = self.get_out_of_stock_products()
        stats['out_of_stock_count'] = len(out_of_stock)
        
        # Total inventory value
        value_result = db.execute_query("SELECT SUM(price * quantity) as total_value FROM products")
        stats['total_inventory_value'] = value_result[0]['total_value'] if value_result and value_result[0]['total_value'] else 0
        
        return stats

# Global product management service instance
product_service = ProductManagementService()
