# Product Management GUI for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from user_management import user_service
from product_management import product_service
from models import Product, Category
from config import GUI_CONFIG, USER_ROLES
from typing import Optional

class ProductManagementFrame:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        
        # Current selected product
        self.selected_product = None
        
        # Create product management interface
        self.create_interface()
        
        # Load initial data
        self.refresh_data()
    
    def create_interface(self):
        """Create the product management interface"""
        # Title and controls
        self.create_header()
        
        # Main content area with notebook
        self.create_notebook()
        
        # Product list tab
        self.create_product_list_tab()
        
        # Category management tab
        self.create_category_tab()
        
        # Stock management tab
        self.create_stock_tab()
    
    def create_header(self):
        """Create header with title and main controls"""
        header_frame = ttk.Frame(self.frame)
        header_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['medium'])
        
        # Title
        ttk.Label(header_frame, text="Product Management", 
                 font=GUI_CONFIG['fonts']['title']).pack(side=tk.LEFT)
        
        # Main action buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT)
        
        if user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            ttk.Button(button_frame, text="Add Product", 
                      command=self.add_product).pack(side=tk.LEFT, 
                                                    padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(button_frame, text="Refresh", 
                  command=self.refresh_data).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
    
    def create_notebook(self):
        """Create notebook for different tabs"""
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, 
                          padx=GUI_CONFIG['padding']['medium'], 
                          pady=GUI_CONFIG['padding']['small'])
    
    def create_product_list_tab(self):
        """Create product list and management tab"""
        # Create tab frame
        self.product_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.product_tab, text="Products")
        
        # Search frame
        search_frame = ttk.Frame(self.product_tab)
        search_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['small'])
        
        ttk.Label(search_frame, text="Search:").pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(search_frame, text="Clear", 
                  command=self.clear_search).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
        
        # Product list frame
        list_frame = ttk.Frame(self.product_tab)
        list_frame.pack(fill=tk.BOTH, expand=True, 
                       padx=GUI_CONFIG['padding']['medium'], 
                       pady=GUI_CONFIG['padding']['small'])
        
        # Create treeview for products
        columns = ('ID', 'Name', 'SKU', 'Category', 'Price', 'Quantity', 'Min Stock', 'Status')
        self.product_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Name': 150, 'SKU': 100, 'Category': 120, 
                        'Price': 80, 'Quantity': 80, 'Min Stock': 80, 'Status': 100}
        
        for col in columns:
            self.product_tree.heading(col, text=col, command=lambda c=col: self.sort_products(c))
            self.product_tree.column(col, width=column_widths.get(col, 100))
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.product_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.product_tree.xview)
        self.product_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.product_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind selection event
        self.product_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.product_tree.bind('<Double-1>', self.edit_product)
        
        # Action buttons frame
        action_frame = ttk.Frame(self.product_tab)
        action_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['small'])
        
        if user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            ttk.Button(action_frame, text="Edit Product", 
                      command=self.edit_product).pack(side=tk.LEFT, 
                                                     padx=GUI_CONFIG['padding']['small'])
            
            ttk.Button(action_frame, text="Delete Product", 
                      command=self.delete_product).pack(side=tk.LEFT, 
                                                       padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="View Details", 
                  command=self.view_product_details).pack(side=tk.LEFT, 
                                                         padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(action_frame, text="Transaction History", 
                  command=self.view_transaction_history).pack(side=tk.LEFT, 
                                                            padx=GUI_CONFIG['padding']['small'])
    
    def create_category_tab(self):
        """Create category management tab"""
        self.category_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.category_tab, text="Categories")
        
        # Category list frame
        cat_list_frame = ttk.LabelFrame(self.category_tab, text="Categories", 
                                       padding=GUI_CONFIG['padding']['medium'])
        cat_list_frame.pack(fill=tk.BOTH, expand=True, 
                           padx=GUI_CONFIG['padding']['medium'], 
                           pady=GUI_CONFIG['padding']['medium'])
        
        # Category treeview
        cat_columns = ('ID', 'Name', 'Description', 'Product Count')
        self.category_tree = ttk.Treeview(cat_list_frame, columns=cat_columns, 
                                         show='headings', height=10)
        
        for col in cat_columns:
            self.category_tree.heading(col, text=col)
            self.category_tree.column(col, width=150)
        
        self.category_tree.pack(fill=tk.BOTH, expand=True)
        
        # Category action buttons
        cat_button_frame = ttk.Frame(self.category_tab)
        cat_button_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                             pady=GUI_CONFIG['padding']['small'])
        
        if user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            ttk.Button(cat_button_frame, text="Add Category", 
                      command=self.add_category).pack(side=tk.LEFT, 
                                                     padx=GUI_CONFIG['padding']['small'])
            
            ttk.Button(cat_button_frame, text="Edit Category", 
                      command=self.edit_category).pack(side=tk.LEFT, 
                                                      padx=GUI_CONFIG['padding']['small'])
            
            ttk.Button(cat_button_frame, text="Delete Category", 
                      command=self.delete_category).pack(side=tk.LEFT, 
                                                        padx=GUI_CONFIG['padding']['small'])
    
    def create_stock_tab(self):
        """Create stock management tab"""
        self.stock_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.stock_tab, text="Stock Management")
        
        # Stock adjustment frame
        stock_frame = ttk.LabelFrame(self.stock_tab, text="Stock Adjustment", 
                                    padding=GUI_CONFIG['padding']['medium'])
        stock_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                        pady=GUI_CONFIG['padding']['medium'])
        
        # Product selection
        ttk.Label(stock_frame, text="Select Product:").grid(row=0, column=0, sticky='w', 
                                                           pady=GUI_CONFIG['padding']['small'])
        
        self.stock_product_var = tk.StringVar()
        self.stock_product_combo = ttk.Combobox(stock_frame, textvariable=self.stock_product_var, 
                                               state='readonly', width=40)
        self.stock_product_combo.grid(row=0, column=1, columnspan=2, sticky='ew', 
                                     padx=GUI_CONFIG['padding']['small'], 
                                     pady=GUI_CONFIG['padding']['small'])
        
        # Current stock display
        ttk.Label(stock_frame, text="Current Stock:").grid(row=1, column=0, sticky='w', 
                                                          pady=GUI_CONFIG['padding']['small'])
        
        self.current_stock_var = tk.StringVar(value="Select a product")
        ttk.Label(stock_frame, textvariable=self.current_stock_var).grid(row=1, column=1, sticky='w', 
                                                                        padx=GUI_CONFIG['padding']['small'])
        
        # Adjustment type
        ttk.Label(stock_frame, text="Adjustment Type:").grid(row=2, column=0, sticky='w', 
                                                            pady=GUI_CONFIG['padding']['small'])
        
        self.adjustment_type_var = tk.StringVar(value="Stock In")
        adjustment_frame = ttk.Frame(stock_frame)
        adjustment_frame.grid(row=2, column=1, sticky='w', padx=GUI_CONFIG['padding']['small'])
        
        ttk.Radiobutton(adjustment_frame, text="Stock In", variable=self.adjustment_type_var, 
                       value="Stock In").pack(side=tk.LEFT)
        ttk.Radiobutton(adjustment_frame, text="Stock Out", variable=self.adjustment_type_var, 
                       value="Stock Out").pack(side=tk.LEFT, padx=GUI_CONFIG['padding']['small'])
        
        # Quantity
        ttk.Label(stock_frame, text="Quantity:").grid(row=3, column=0, sticky='w', 
                                                     pady=GUI_CONFIG['padding']['small'])
        
        self.quantity_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.quantity_var, width=20).grid(row=3, column=1, sticky='w', 
                                                                             padx=GUI_CONFIG['padding']['small'])
        
        # Price (optional)
        ttk.Label(stock_frame, text="Unit Price (optional):").grid(row=4, column=0, sticky='w', 
                                                                  pady=GUI_CONFIG['padding']['small'])
        
        self.price_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.price_var, width=20).grid(row=4, column=1, sticky='w', 
                                                                          padx=GUI_CONFIG['padding']['small'])
        
        # Notes
        ttk.Label(stock_frame, text="Notes:").grid(row=5, column=0, sticky='nw', 
                                                  pady=GUI_CONFIG['padding']['small'])
        
        self.notes_var = tk.StringVar()
        ttk.Entry(stock_frame, textvariable=self.notes_var, width=40).grid(row=5, column=1, columnspan=2, 
                                                                          sticky='ew', 
                                                                          padx=GUI_CONFIG['padding']['small'])
        
        # Submit button
        if user_service.has_permission(USER_ROLES['INVENTORY_MANAGER']):
            ttk.Button(stock_frame, text="Apply Stock Adjustment", 
                      command=self.apply_stock_adjustment).grid(row=6, column=1, 
                                                               pady=GUI_CONFIG['padding']['medium'])
        
        # Configure grid weights
        stock_frame.columnconfigure(1, weight=1)
        
        # Bind product selection change
        self.stock_product_combo.bind('<<ComboboxSelected>>', self.on_stock_product_change)
    
    def refresh_data(self):
        """Refresh all data in the interface"""
        self.load_products()
        self.load_categories()
        self.load_stock_products()

    def load_products(self):
        """Load products into the treeview"""
        # Clear existing items
        for item in self.product_tree.get_children():
            self.product_tree.delete(item)

        try:
            products = product_service.get_all_products()
            categories = {cat.id: cat.name for cat in product_service.get_all_categories()}

            for product in products:
                # Determine stock status
                if product.quantity <= 0:
                    status = "Out of Stock"
                elif product.quantity <= product.min_stock_level:
                    status = "Low Stock"
                else:
                    status = "In Stock"

                # Get category name
                category_name = categories.get(product.category_id, "Uncategorized")

                # Insert into treeview
                self.product_tree.insert('', 'end', values=(
                    product.id,
                    product.name,
                    product.sku,
                    category_name,
                    f"${product.price:.2f}",
                    product.quantity,
                    product.min_stock_level,
                    status
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products: {str(e)}")

    def load_categories(self):
        """Load categories into the category treeview"""
        # Clear existing items
        for item in self.category_tree.get_children():
            self.category_tree.delete(item)

        try:
            categories = product_service.get_all_categories()

            for category in categories:
                # Count products in category
                products_in_category = product_service.get_products_by_category(category.id)
                product_count = len(products_in_category)

                self.category_tree.insert('', 'end', values=(
                    category.id,
                    category.name,
                    category.description or "",
                    product_count
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load categories: {str(e)}")

    def load_stock_products(self):
        """Load products into stock management combobox"""
        try:
            products = product_service.get_all_products()
            product_list = [f"{product.name} ({product.sku})" for product in products]
            self.stock_product_combo['values'] = product_list
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load products for stock management: {str(e)}")

    def on_search_change(self, *args):
        """Handle search text change"""
        search_term = self.search_var.get().strip()
        if search_term:
            self.search_products(search_term)
        else:
            self.load_products()

    def search_products(self, query: str):
        """Search and display products"""
        # Clear existing items
        for item in self.product_tree.get_children():
            self.product_tree.delete(item)

        try:
            products = product_service.search_products(query)
            categories = {cat.id: cat.name for cat in product_service.get_all_categories()}

            for product in products:
                # Determine stock status
                if product.quantity <= 0:
                    status = "Out of Stock"
                elif product.quantity <= product.min_stock_level:
                    status = "Low Stock"
                else:
                    status = "In Stock"

                category_name = categories.get(product.category_id, "Uncategorized")

                self.product_tree.insert('', 'end', values=(
                    product.id,
                    product.name,
                    product.sku,
                    category_name,
                    f"${product.price:.2f}",
                    product.quantity,
                    product.min_stock_level,
                    status
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Search failed: {str(e)}")

    def clear_search(self):
        """Clear search and reload all products"""
        self.search_var.set("")
        self.load_products()

    def on_product_select(self, event):
        """Handle product selection"""
        selection = self.product_tree.selection()
        if selection:
            item = self.product_tree.item(selection[0])
            product_id = item['values'][0]
            self.selected_product = product_service.get_product_by_id(product_id)

    def sort_products(self, column):
        """Sort products by column"""
        # This is a placeholder for sorting functionality
        pass

    def add_product(self):
        """Add a new product"""
        dialog = ProductDialog(self.frame, "Add Product")
        if dialog.result:
            try:
                success = product_service.create_product(
                    name=dialog.result['name'],
                    description=dialog.result['description'],
                    category_id=dialog.result['category_id'],
                    sku=dialog.result['sku'],
                    price=dialog.result['price'],
                    quantity=dialog.result['quantity'],
                    min_stock_level=dialog.result['min_stock_level'],
                    supplier_id=dialog.result['supplier_id']
                )

                if success:
                    messagebox.showinfo("Success", "Product added successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to add product")
            except ValueError as e:
                messagebox.showerror("Error", str(e))
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def edit_product(self, event=None):
        """Edit selected product"""
        if not self.selected_product:
            messagebox.showwarning("Warning", "Please select a product to edit")
            return

        dialog = ProductDialog(self.frame, "Edit Product", self.selected_product)
        if dialog.result:
            try:
                success = product_service.update_product(
                    product_id=self.selected_product.id,
                    name=dialog.result['name'],
                    description=dialog.result['description'],
                    category_id=dialog.result['category_id'],
                    price=dialog.result['price'],
                    min_stock_level=dialog.result['min_stock_level'],
                    supplier_id=dialog.result['supplier_id']
                )

                if success:
                    messagebox.showinfo("Success", "Product updated successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to update product")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def delete_product(self):
        """Delete selected product"""
        if not self.selected_product:
            messagebox.showwarning("Warning", "Please select a product to delete")
            return

        if not user_service.has_permission(USER_ROLES['ADMIN']):
            messagebox.showerror("Access Denied", "Only administrators can delete products")
            return

        confirm = messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to delete '{self.selected_product.name}'?\n\nThis action cannot be undone."
        )

        if confirm:
            try:
                success = product_service.delete_product(self.selected_product.id)
                if success:
                    messagebox.showinfo("Success", "Product deleted successfully!")
                    self.selected_product = None
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to delete product")
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def view_product_details(self):
        """View detailed product information"""
        if not self.selected_product:
            messagebox.showwarning("Warning", "Please select a product to view details")
            return

        ProductDetailsDialog(self.frame, self.selected_product)

    def view_transaction_history(self):
        """View transaction history for selected product"""
        if not self.selected_product:
            messagebox.showwarning("Warning", "Please select a product to view transaction history")
            return

        TransactionHistoryDialog(self.frame, self.selected_product)

    def add_category(self):
        """Add a new category"""
        dialog = CategoryDialog(self.frame, "Add Category")
        if dialog.result:
            try:
                success = product_service.create_category(
                    name=dialog.result['name'],
                    description=dialog.result['description']
                )

                if success:
                    messagebox.showinfo("Success", "Category added successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to add category")
            except ValueError as e:
                messagebox.showerror("Error", str(e))
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def edit_category(self):
        """Edit selected category"""
        selection = self.category_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a category to edit")
            return

        item = self.category_tree.item(selection[0])
        category_id = item['values'][0]
        category = product_service.get_category_by_id(category_id)

        if category:
            dialog = CategoryDialog(self.frame, "Edit Category", category)
            if dialog.result:
                try:
                    success = product_service.update_category(
                        category_id=category.id,
                        name=dialog.result['name'],
                        description=dialog.result['description']
                    )

                    if success:
                        messagebox.showinfo("Success", "Category updated successfully!")
                        self.refresh_data()
                    else:
                        messagebox.showerror("Error", "Failed to update category")
                except Exception as e:
                    messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def delete_category(self):
        """Delete selected category"""
        selection = self.category_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a category to delete")
            return

        if not user_service.has_permission(USER_ROLES['ADMIN']):
            messagebox.showerror("Access Denied", "Only administrators can delete categories")
            return

        item = self.category_tree.item(selection[0])
        category_id = item['values'][0]
        category_name = item['values'][1]

        confirm = messagebox.askyesno(
            "Confirm Delete",
            f"Are you sure you want to delete category '{category_name}'?\n\nThis action cannot be undone."
        )

        if confirm:
            try:
                success = product_service.delete_category(category_id)
                if success:
                    messagebox.showinfo("Success", "Category deleted successfully!")
                    self.refresh_data()
                else:
                    messagebox.showerror("Error", "Failed to delete category")
            except ValueError as e:
                messagebox.showerror("Error", str(e))
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def on_stock_product_change(self, event=None):
        """Handle stock product selection change"""
        selected = self.stock_product_var.get()
        if selected:
            # Extract SKU from selection
            sku = selected.split('(')[-1].rstrip(')')
            products = product_service.get_all_products()

            for product in products:
                if product.sku == sku:
                    self.current_stock_var.set(f"{product.quantity} units")
                    break

    def apply_stock_adjustment(self):
        """Apply stock adjustment"""
        selected = self.stock_product_var.get()
        if not selected:
            messagebox.showwarning("Warning", "Please select a product")
            return

        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("Error", "Quantity must be greater than 0")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid quantity")
            return

        # Get price if provided
        price = None
        if self.price_var.get().strip():
            try:
                price = float(self.price_var.get())
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid price")
                return

        # Extract SKU and find product
        sku = selected.split('(')[-1].rstrip(')')
        products = product_service.get_all_products()
        product = None

        for p in products:
            if p.sku == sku:
                product = p
                break

        if not product:
            messagebox.showerror("Error", "Product not found")
            return

        try:
            adjustment_type = self.adjustment_type_var.get()
            notes = self.notes_var.get().strip()

            if adjustment_type == "Stock In":
                success = product_service.stock_in(product.id, quantity, price, notes)
            else:
                success = product_service.stock_out(product.id, quantity, price, notes)

            if success:
                messagebox.showinfo("Success", f"Stock adjustment applied successfully!")
                # Clear form
                self.quantity_var.set("")
                self.price_var.set("")
                self.notes_var.set("")
                # Refresh data
                self.refresh_data()
                self.on_stock_product_change()
            else:
                messagebox.showerror("Error", "Failed to apply stock adjustment")

        except ValueError as e:
            messagebox.showerror("Error", str(e))
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def get_frame(self):
        """Get the main frame"""
        return self.frame
