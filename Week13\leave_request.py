import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import json
import uuid

class LeaveRequestManager:
    def __init__(self):
        self.requests = []
    
    def submit_request(self, staff_id, leave_type, start_date, end_date):
        request = {
            'id': str(uuid.uuid4())[:8],
            'staff_id': staff_id,
            'leave_type': leave_type,
            'start_date': start_date,
            'end_date': end_date,
            'status': 'Pending',
            'reason': '',
            'submission_date': datetime.now().strftime('%Y-%m-%d')
        }
        self.requests.append(request)
        return request['id']
    
    def get_pending_requests(self):
        return [req for req in self.requests if req['status'] == 'Pending']
    
    def approve_request(self, request_id):
        for req in self.requests:
            if req['id'] == request_id:
                req['status'] = 'Approved'
                return True
        return False
    
    def reject_request(self, request_id, reason):
        for req in self.requests:
            if req['id'] == request_id:
                req['status'] = 'Rejected'
                req['reason'] = reason
                return True
        return False
    
    def get_staff_requests(self, staff_id):
        return [req for req in self.requests if req['staff_id'] == staff_id]
    
    def get_request_duration(self, request_id):
        for req in self.requests:
            if req['id'] == request_id:
                start = datetime.strptime(req['start_date'], '%Y-%m-%d')
                end = datetime.strptime(req['end_date'], '%Y-%m-%d')
                return (end - start).days + 1
        return 0
    
    def get_annual_approved_summary(self, year=None):
        if year is None:
            year = datetime.now().year
        
        summary = {}
        for req in self.requests:
            if req['status'] == 'Approved':
                req_year = datetime.strptime(req['start_date'], '%Y-%m-%d').year
                if req_year == year:
                    staff_id = req['staff_id']
                    days = self.get_request_duration(req['id'])
                    summary[staff_id] = summary.get(staff_id, 0) + days
        return summary
    
    def update_request(self, request_id, new_start, new_end):
        for req in self.requests:
            if req['id'] == request_id and req['status'] == 'Pending':
                req['start_date'] = new_start
                req['end_date'] = new_end
                return True
        return False
    
    def export_annual_report(self, year, filename):
        annual_requests = [
            req for req in self.requests 
            if datetime.strptime(req['start_date'], '%Y-%m-%d').year == year
        ]
        
        try:
            with open(filename, 'w') as f:
                json.dump(annual_requests, f, indent=2)
            return True
        except:
            return False

class LeaveRequestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Staff Leave Request System")
        self.root.geometry("1200x800")
        
        self.manager = LeaveRequestManager()
        
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_submit_tab()
        self.create_manage_tab()
        self.create_view_tab()
        self.create_reports_tab()
        
        # Load sample data
        self.load_sample_data()
    
    def create_submit_tab(self):
        # Submit Request Tab
        submit_frame = ttk.Frame(self.notebook)
        self.notebook.add(submit_frame, text="Submit Request")
        
        # Title
        title_label = ttk.Label(submit_frame, text="Submit Leave Request", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # Form frame
        form_frame = ttk.LabelFrame(submit_frame, text="Request Details", padding=30)
        form_frame.pack(pady=20, padx=50, fill=tk.X)
        
        # Staff ID Selection
        ttk.Label(form_frame, text="Select Staff ID:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=10)
        self.staff_id_combo = ttk.Combobox(form_frame, width=20, state="readonly")
        self.staff_id_combo['values'] = ('EMP001 - John Smith', 'EMP002 - Jane Doe', 'EMP003 - Mike Johnson', 
                                         'EMP004 - Sarah Wilson', 'EMP005 - David Brown', 'EMP006 - Lisa Davis',
                                         'EMP007 - Tom Anderson', 'EMP008 - Mary Taylor', 'EMP009 - Chris Lee',
                                         'EMP010 - Emma Garcia')
        self.staff_id_combo.grid(row=0, column=1, pady=10, padx=10, sticky=tk.W)
        
        # Leave Type Selection
        ttk.Label(form_frame, text="Select Leave Type:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=10)
        self.leave_type_combo = ttk.Combobox(form_frame, width=20, state="readonly")
        self.leave_type_combo['values'] = ('Annual Leave', 'Sick Leave', 'Personal Leave', 
                                          'Maternity Leave', 'Emergency Leave', 'Compassionate Leave',
                                          'Study Leave', 'Unpaid Leave')
        self.leave_type_combo.grid(row=1, column=1, pady=10, padx=10, sticky=tk.W)
        
        # From Date Selection
        ttk.Label(form_frame, text="From Date:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=10)
        date_from_frame = ttk.Frame(form_frame)
        date_from_frame.grid(row=2, column=1, pady=10, padx=10, sticky=tk.W)
        
        # From Day dropdown
        self.from_day_combo = ttk.Combobox(date_from_frame, width=5, state="readonly")
        self.from_day_combo['values'] = [str(i).zfill(2) for i in range(1, 32)]
        self.from_day_combo.pack(side=tk.LEFT, padx=(0, 5))
        
        # From Month dropdown
        self.from_month_combo = ttk.Combobox(date_from_frame, width=12, state="readonly")
        self.from_month_combo['values'] = ('01-January', '02-February', '03-March', '04-April', 
                                          '05-May', '06-June', '07-July', '08-August',
                                          '09-September', '10-October', '11-November', '12-December')
        self.from_month_combo.pack(side=tk.LEFT, padx=5)
        
        # From Year dropdown
        current_year = datetime.now().year
        self.from_year_combo = ttk.Combobox(date_from_frame, width=8, state="readonly")
        self.from_year_combo['values'] = [str(year) for year in range(current_year, current_year + 3)]
        self.from_year_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # To Date Selection
        ttk.Label(form_frame, text="To Date:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=10)
        date_to_frame = ttk.Frame(form_frame)
        date_to_frame.grid(row=3, column=1, pady=10, padx=10, sticky=tk.W)
        
        # To Day dropdown
        self.to_day_combo = ttk.Combobox(date_to_frame, width=5, state="readonly")
        self.to_day_combo['values'] = [str(i).zfill(2) for i in range(1, 32)]
        self.to_day_combo.pack(side=tk.LEFT, padx=(0, 5))
        
        # To Month dropdown
        self.to_month_combo = ttk.Combobox(date_to_frame, width=12, state="readonly")
        self.to_month_combo['values'] = ('01-January', '02-February', '03-March', '04-April', 
                                        '05-May', '06-June', '07-July', '08-August',
                                        '09-September', '10-October', '11-November', '12-December')
        self.to_month_combo.pack(side=tk.LEFT, padx=5)
        
        # To Year dropdown
        self.to_year_combo = ttk.Combobox(date_to_frame, width=8, state="readonly")
        self.to_year_combo['values'] = [str(year) for year in range(current_year, current_year + 3)]
        self.to_year_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # Number of Leave Days (Auto-calculated)
        ttk.Label(form_frame, text="Number of Leave Days:", font=('Arial', 10, 'bold')).grid(row=4, column=0, sticky=tk.W, pady=10)
        self.days_display_label = ttk.Label(form_frame, text="Select dates first", 
                                           font=('Arial', 10), foreground='blue')
        self.days_display_label.grid(row=4, column=1, pady=10, padx=10, sticky=tk.W)
        
        # Bind events to calculate days when dates change
        self.from_day_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        self.from_month_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        self.from_year_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        self.to_day_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        self.to_month_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        self.to_year_combo.bind('<<ComboboxSelected>>', self.calculate_days)
        
        # Submit Button
        submit_btn = ttk.Button(form_frame, text="Submit Leave Request", 
                               command=self.submit_request, style='Accent.TButton')
        submit_btn.grid(row=5, column=0, columnspan=2, pady=30)
        
        # Set default values
        self.from_day_combo.set(str(datetime.now().day).zfill(2))
        self.from_month_combo.set(f"{datetime.now().month:02d}-{datetime.now().strftime('%B')}")
        self.from_year_combo.set(str(datetime.now().year))
        self.leave_days_var.set("1")
    
    def create_manage_tab(self):
        # Manage Requests Tab
        manage_frame = ttk.Frame(self.notebook)
        self.notebook.add(manage_frame, text="Manage Requests")
        
        # Title
        title_label = ttk.Label(manage_frame, text="Manage Leave Requests", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # Buttons frame
        btn_frame = ttk.Frame(manage_frame)
        btn_frame.pack(pady=10)
        
        ttk.Button(btn_frame, text="Refresh", command=self.refresh_pending).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Approve Selected", command=self.approve_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Reject Selected", command=self.reject_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="Update Selected", command=self.update_selected).pack(side=tk.LEFT, padx=5)
        
        # Treeview for pending requests
        columns = ('ID', 'Staff ID', 'Leave Type', 'Start Date', 'End Date', 'Days', 'Status')
        self.pending_tree = ttk.Treeview(manage_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=120)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(manage_frame, orient=tk.VERTICAL, command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=scrollbar.set)
        
        self.pending_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 0), pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 20), pady=20)
    
    def create_view_tab(self):
        # View Requests Tab
        view_frame = ttk.Frame(self.notebook)
        self.notebook.add(view_frame, text="View Requests")
        
        # Title
        title_label = ttk.Label(view_frame, text="View All Requests", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # Filter frame
        filter_frame = ttk.LabelFrame(view_frame, text="Filter Options", padding=10)
        filter_frame.pack(pady=10, padx=20, fill=tk.X)
        
        ttk.Label(filter_frame, text="Staff ID:").pack(side=tk.LEFT)
        self.filter_staff_entry = ttk.Entry(filter_frame, width=15)
        self.filter_staff_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(filter_frame, text="Filter by Staff", 
                  command=self.filter_by_staff).pack(side=tk.LEFT, padx=5)
        ttk.Button(filter_frame, text="Show All", 
                  command=self.show_all_requests).pack(side=tk.LEFT, padx=5)
        
        # Treeview for all requests
        columns = ('ID', 'Staff ID', 'Leave Type', 'Start Date', 'End Date', 'Days', 'Status', 'Reason')
        self.all_tree = ttk.Treeview(view_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.all_tree.heading(col, text=col)
            self.all_tree.column(col, width=100)
        
        # Scrollbar for all requests
        scrollbar2 = ttk.Scrollbar(view_frame, orient=tk.VERTICAL, command=self.all_tree.yview)
        self.all_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.all_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 0), pady=20)
        scrollbar2.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 20), pady=20)
    
    def create_reports_tab(self):
        # Reports Tab
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="Reports")
        
        # Title
        title_label = ttk.Label(reports_frame, text="Reports & Analytics", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # Reports options frame
        options_frame = ttk.LabelFrame(reports_frame, text="Report Options", padding=20)
        options_frame.pack(pady=20, padx=50, fill=tk.X)
        
        # Annual Summary
        ttk.Label(options_frame, text="Year for Annual Summary:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.year_entry = ttk.Entry(options_frame, width=10)
        self.year_entry.insert(0, str(datetime.now().year))
        self.year_entry.grid(row=0, column=1, pady=5, padx=10)
        
        ttk.Button(options_frame, text="Generate Annual Summary", 
                  command=self.generate_annual_summary).grid(row=0, column=2, pady=5, padx=10)
        
        # Export Report
        ttk.Label(options_frame, text="Export Year:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.export_year_entry = ttk.Entry(options_frame, width=10)
        self.export_year_entry.insert(0, str(datetime.now().year))
        self.export_year_entry.grid(row=1, column=1, pady=5, padx=10)
        
        ttk.Button(options_frame, text="Export Annual Report", 
                  command=self.export_report).grid(row=1, column=2, pady=5, padx=10)
        
        # Report display area
        self.report_text = tk.Text(reports_frame, height=25, width=80)
        self.report_text.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        # Scrollbar for report text
        report_scrollbar = ttk.Scrollbar(reports_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scrollbar.set)
    
    def on_days_change(self, *args):
        """Update end date when number of days changes"""
        self.update_end_date()
    
    def on_from_date_change(self, event=None):
        """Update end date when from date changes"""
        self.update_end_date()
    
    def update_end_date(self):
        """Calculate and display the end date based on from date and number of days"""
        try:
            # Get values
            day = self.from_day_combo.get()
            month_val = self.from_month_combo.get()
            year = self.from_year_combo.get()
            days = self.leave_days_var.get()
            
            if not all([day, month_val, year, days]):
                self.to_date_label.config(text="Select from date and days first")
                return
            
            # Extract month number from "MM-MonthName" format
            month = month_val.split('-')[0]
            
            # Create start date
            start_date = datetime(int(year), int(month), int(day))
            
            # Calculate end date (number of days - 1 because it's inclusive)
            end_date = start_date + timedelta(days=int(days) - 1)
            
            # Display end date
            end_date_str = end_date.strftime('%d-%B-%Y')
            self.to_date_label.config(text=end_date_str, foreground='green')
            
        except (ValueError, IndexError):
            self.to_date_label.config(text="Invalid date selection", foreground='red')
    
    def get_date_string(self, day_combo, month_combo, year_combo):
        """Convert dropdown selections to YYYY-MM-DD format"""
        try:
            day = day_combo.get()
            month_val = month_combo.get()
            year = year_combo.get()
            
            if not all([day, month_val, year]):
                return None
            
            month = month_val.split('-')[0]
            return f"{year}-{month}-{day}"
        except:
            return None
        try:
            staff_id = self.staff_id_entry.get().strip()
            leave_type = self.leave_type_combo.get()
            start_date = self.start_date_entry.get().strip()
            end_date = self.end_date_entry.get().strip()
            
            if not all([staff_id, leave_type, start_date, end_date]):
                messagebox.showerror("Error", "Please fill in all fields")
                return
            
            # Validate date format
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
            
            request_id = self.manager.submit_request(staff_id, leave_type, start_date, end_date)
            messagebox.showinfo("Success", f"Request submitted successfully!\nRequest ID: {request_id}")
            
            # Clear form
            self.staff_id_entry.delete(0, tk.END)
            self.leave_type_combo.set('')
            self.start_date_entry.delete(0, tk.END)
            self.end_date_entry.delete(0, tk.END)
            
            # Refresh displays
            self.refresh_pending()
            self.show_all_requests()
            
        except ValueError:
            messagebox.showerror("Error", "Please use YYYY-MM-DD format for dates")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
    
    def refresh_pending(self):
        # Clear existing items
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)
        
        # Add pending requests
        pending_requests = self.manager.get_pending_requests()
        for req in pending_requests:
            days = self.manager.get_request_duration(req['id'])
            self.pending_tree.insert('', tk.END, values=(
                req['id'], req['staff_id'], req['leave_type'],
                req['start_date'], req['end_date'], days, req['status']
            ))
    
    def approve_selected(self):
        selection = self.pending_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a request to approve")
            return
        
        item = self.pending_tree.item(selection[0])
        request_id = item['values'][0]
        
        if self.manager.approve_request(request_id):
            messagebox.showinfo("Success", "Request approved successfully")
            self.refresh_pending()
            self.show_all_requests()
        else:
            messagebox.showerror("Error", "Failed to approve request")
    
    def reject_selected(self):
        selection = self.pending_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a request to reject")
            return
        
        item = self.pending_tree.item(selection[0])
        request_id = item['values'][0]
        
        # Get rejection reason
        reason = tk.simpledialog.askstring("Rejection Reason", "Enter reason for rejection:")
        if reason:
            if self.manager.reject_request(request_id, reason):
                messagebox.showinfo("Success", "Request rejected successfully")
                self.refresh_pending()
                self.show_all_requests()
            else:
                messagebox.showerror("Error", "Failed to reject request")
    
    def update_selected(self):
        selection = self.pending_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a request to update")
            return
        
        item = self.pending_tree.item(selection[0])
        request_id = item['values'][0]
        
        # Create update window
        update_window = tk.Toplevel(self.root)
        update_window.title("Update Request")
        update_window.geometry("400x200")
        
        ttk.Label(update_window, text="New Start Date (YYYY-MM-DD):").pack(pady=10)
        new_start_entry = ttk.Entry(update_window, width=20)
        new_start_entry.pack(pady=5)
        new_start_entry.insert(0, item['values'][3])
        
        ttk.Label(update_window, text="New End Date (YYYY-MM-DD):").pack(pady=10)
        new_end_entry = ttk.Entry(update_window, width=20)
        new_end_entry.pack(pady=5)
        new_end_entry.insert(0, item['values'][4])
        
        def update_request():
            try:
                new_start = new_start_entry.get().strip()
                new_end = new_end_entry.get().strip()
                
                # Validate date format
                datetime.strptime(new_start, '%Y-%m-%d')
                datetime.strptime(new_end, '%Y-%m-%d')
                
                if self.manager.update_request(request_id, new_start, new_end):
                    messagebox.showinfo("Success", "Request updated successfully")
                    update_window.destroy()
                    self.refresh_pending()
                    self.show_all_requests()
                else:
                    messagebox.showerror("Error", "Failed to update request")
            except ValueError:
                messagebox.showerror("Error", "Please use YYYY-MM-DD format for dates")
        
        ttk.Button(update_window, text="Update", command=update_request).pack(pady=20)
    
    def show_all_requests(self):
        # Clear existing items
        for item in self.all_tree.get_children():
            self.all_tree.delete(item)
        
        # Add all requests
        for req in self.manager.requests:
            days = self.manager.get_request_duration(req['id'])
            self.all_tree.insert('', tk.END, values=(
                req['id'], req['staff_id'], req['leave_type'],
                req['start_date'], req['end_date'], days, 
                req['status'], req['reason']
            ))
    
    def filter_by_staff(self):
        staff_id = self.filter_staff_entry.get().strip()
        if not staff_id:
            messagebox.showwarning("Warning", "Please enter a Staff ID")
            return
        
        # Clear existing items
        for item in self.all_tree.get_children():
            self.all_tree.delete(item)
        
        # Add filtered requests
        staff_requests = self.manager.get_staff_requests(staff_id)
        for req in staff_requests:
            days = self.manager.get_request_duration(req['id'])
            self.all_tree.insert('', tk.END, values=(
                req['id'], req['staff_id'], req['leave_type'],
                req['start_date'], req['end_date'], days, 
                req['status'], req['reason']
            ))
    
    def generate_annual_summary(self):
        try:
            year = int(self.year_entry.get())
            summary = self.manager.get_annual_approved_summary(year)
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(tk.END, f"Annual Approved Leave Summary - {year}\n")
            self.report_text.insert(tk.END, "="*50 + "\n\n")
            
            if summary:
                total_days = 0
                for staff_id, days in summary.items():
                    self.report_text.insert(tk.END, f"Staff ID: {staff_id} - {days} days\n")
                    total_days += days
                
                self.report_text.insert(tk.END, f"\nTotal approved days: {total_days}\n")
                self.report_text.insert(tk.END, f"Average per staff member: {total_days/len(summary):.1f} days\n")
            else:
                self.report_text.insert(tk.END, "No approved leave requests found for this year.\n")
        
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid year")
    
    def export_report(self):
        try:
            year = int(self.export_year_entry.get())
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if filename:
                if self.manager.export_annual_report(year, filename):
                    messagebox.showinfo("Success", f"Report exported to {filename}")
                else:
                    messagebox.showerror("Error", "Failed to export report")
        
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid year")
    
    def load_sample_data(self):
        # Load some sample data for demonstration
        sample_requests = [
            ("EMP001", "Annual Leave", "2024-12-01", "2024-12-05"),
            ("EMP002", "Sick Leave", "2024-11-15", "2024-11-17"),
            ("EMP001", "Personal Leave", "2024-10-20", "2024-10-20"),
            ("EMP003", "Annual Leave", "2024-12-15", "2024-12-22"),
        ]
        
        for staff_id, leave_type, start_date, end_date in sample_requests:
            self.manager.submit_request(staff_id, leave_type, start_date, end_date)
        
        # Approve some requests
        if self.manager.requests:
            self.manager.approve_request(self.manager.requests[0]['id'])
            self.manager.reject_request(self.manager.requests[1]['id'], "Insufficient sick leave balance")
        
        # Refresh displays
        self.refresh_pending()
        self.show_all_requests()

# Import tkinter.simpledialog for rejection reason input
import tkinter.simpledialog

if __name__ == "__main__":
    root = tk.Tk()
    app = LeaveRequestGUI(root)
    root.mainloop()