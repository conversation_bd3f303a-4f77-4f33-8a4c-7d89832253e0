{"cells": [{"cell_type": "code", "execution_count": 3, "id": "f8422c8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["first, Second and Third\n", "first, Second and Third\n", "Third, first and Second\n"]}], "source": ["print (\"{}, {} and {}\".format(\"first\", \"Second\", \"Third\"))\n", "print (\"{0}, {1} and {2}\".format(\"first\", \"Second\", \"Third\"))\n", "# print (\"{0}, {1} and {2}\".format(\"first\", \"Second\", \"Third\"))\n", "print (\"{2}, {0} and {1}\".format(\"first\", \"Second\", \"Third\"))\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "19f9a8ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello,<PERSON>!\n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n"]}], "source": ["print (\"{txt1},{txt2}!\".format(txt1 = \"Hello\", txt2= \"World\"))\n", "print (\"{x}{}{y}{}\".format(\"hi\",\"Hello\", x = \"Hey\", y= \"Yoo\"))\n"]}, {"cell_type": "code", "execution_count": 10, "id": "d9e1a539", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello,<PERSON><PERSON>!\n"]}], "source": ["database = [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"]\n", "print(\"Hello,{name[0]}!\".format(name=database))"]}, {"cell_type": "code", "execution_count": 12, "id": "6aaa05f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I earned 200 US Dollar\n", "On everage, a/an Elephant weighs 4000 Kg\n"]}], "source": ["profit = 200\n", "print(f\"I earned {profit} US Dollar\")\n", "animal = \"Elephant\"\n", "w = 4000\n", "print(f'On everage, a/an {animal} weighs {w} Kg')"]}, {"cell_type": "code", "execution_count": null, "id": "5a4e028e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["111 decimal is 1101111 in binary\n", "111 decimal is 6f in hexadecimal.\n"]}], "source": ["i = 111\n", "print(f\"{i} decimal is {i:b} in binary\")\n", "print(f\"{i} decimal is {i:x} in hexadecimal.\")  "]}, {"cell_type": "code", "execution_count": null, "id": "07660031", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                 123\n", "<PERSON><PERSON>       \n"]}], "source": ["# number add the space from left\n", "num = 123\n", "print(f\"{num:20}\")\n", "# number add the space from right\n", "name = \"<PERSON><PERSON>\"\n", "print(f\"{name:10}\")"]}, {"cell_type": "code", "execution_count": null, "id": "c6934c1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["USAA!\n", ">10 divided by 3 equals 3.3333333333333335\n", "10 divided by 3 equals 3.333333\n", "10 divided by 3 equals 3.333333\n"]}], "source": ["div =  10/3\n", "print (\"USAA!\")\n", "print(f\"10 divided by 3 equals {div}\")\n", "print(f\"10 divided by 3 equals {div:f}\")\n", "print(f\"10 divided by 3 equals {div:2f}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "f129742d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey, USAA!I won 10,000,000 Us Dollars\n"]}], "source": ["prize = 10000000\n", "name = \"USAA!\"\n", "print(f\"Hey, {name}I won {prize:,} Us Dollars\")"]}, {"cell_type": "code", "execution_count": 36, "id": "0c892e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["USAA                \n", "        USAA        \n", "                USAA\n", "\n", "USAA                \n", "        USAA        \n", "                USAA\n"]}], "source": ["txt = \"USAA\"\n", "print(f\"{txt:<20}\")\n", "print(f\"{txt:^20}\")\n", "print(f\"{txt:>20}\")\n", "print(___)\n", "print(f\"{txt:<20}\\n{txt:^20}\\n{txt:>20}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "05de87ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Hello, world!    \n", "$$$$$$WIN WIN $$$$$$\n", "To the left_________\n", "To the right********\n", "++++++++++10 divided by 3 equals3.333333++++++++++\n"]}], "source": ["print(\"Hello, world!\". center(20))\n", "print(\" WIN WIN \".center(20,'$'))\n", "print(\"To the left\".lju<PERSON>(20,'_'))\n", "print(\"To the right\".lju<PERSON>(20,'*'))\n", "div = 10/3\n", "print(f\"10 divided by 3 equals{div:2f}\".center(50,'+'))\n", "\n", "\n", "    \n"]}, {"cell_type": "code", "execution_count": 45, "id": "15e37922", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["We learn Pyton every day!\n", "0\n", "-1\n", "-1\n", "A snack here, and a snack there!\n", "2\n"]}], "source": ["txt1 = \"We learn Pyton every day!\"\n", "print(txt1)\n", "print(txt1.find(\"We\"))\n", "print(txt1.find(\"Python\"))\n", "print(txt1.find(\"snack\"))\n", "txt2 = \"A snack here, and a snack there!\"\n", "print(txt2)\n", "print(txt2.find(\"snack\"))\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}