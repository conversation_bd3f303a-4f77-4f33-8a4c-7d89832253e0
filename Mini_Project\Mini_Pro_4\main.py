class RecipeBook:
    def __init__(self):
        # Dictionary structure: {recipe_name: [[ingredients_list], instructions_string]}
        self.recipes = {}
    
    def display_menu(self):
        """Display the main menu options"""
        print("\n" + "="*50)
        print("🍳 DIGITAL RECIPE BOOK 🍳")
        print("="*50)
        print("1. Add New Recipe")
        print("2. View Recipe Details")
        print("3. Search Recipes by Ingredient")
        print("4. Update Recipe")
        print("5. Delete Recipe")
        print("6. List All Recipes")
        print("7. Exit")
        print("="*50)
    
    def add_recipe(self):
        """Add a new recipe to the book"""
        print("\n📝 ADD NEW RECIPE")
        print("-" * 30)
        
        # Get recipe name
        recipe_name = input("Enter recipe name: ").strip()
        
        if not recipe_name:
            print("❌ Recipe name cannot be empty!")
            return
        
        # Check for duplicate names (case-insensitive)
        if recipe_name.lower() in [name.lower() for name in self.recipes.keys()]:
            print(f"❌ Recipe '{recipe_name}' already exists!")
            return
        
        # Get ingredients
        ingredients_input = input("Enter ingredients (comma-separated): ").strip()
        if not ingredients_input:
            print("❌ Ingredients cannot be empty!")
            return
        
        ingredients = [ingredient.strip() for ingredient in ingredients_input.split(",")]
        ingredients = [ing for ing in ingredients if ing]  # Remove empty strings
        
        if not ingredients:
            print("❌ Please provide valid ingredients!")
            return
        
        # Get instructions
        instructions = input("Enter cooking instructions: ").strip()
        if not instructions:
            print("❌ Instructions cannot be empty!")
            return
        
        # Store the recipe
        self.recipes[recipe_name] = [ingredients, instructions]
        print(f"✅ Recipe '{recipe_name}' added successfully!")
    
    def view_recipe(self):
        """Display details of a specific recipe"""
        print("\n👀 VIEW RECIPE DETAILS")
        print("-" * 30)
        
        if not self.recipes:
            print("📚 No recipes available. Add some recipes first!")
            return
        
        recipe_name = input("Enter recipe name: ").strip()
        
        # Case-insensitive search for recipe
        found_recipe = None
        for name in self.recipes.keys():
            if name.lower() == recipe_name.lower():
                found_recipe = name
                break
        
        if not found_recipe:
            print(f"❌ Recipe '{recipe_name}' not found!")
            return
        
        # Display recipe details
        ingredients, instructions = self.recipes[found_recipe]
        
        print(f"\n🍽  RECIPE: {found_recipe}")
        print("=" * (len(found_recipe) + 12))
        print("\n📋 INGREDIENTS:")
        for i, ingredient in enumerate(ingredients, 1):
            print(f"  {i}. {ingredient}")
        
        print(f"\n📝 INSTRUCTIONS:")
        print(f"  {instructions}")
        print("\n" + "="*40)
    
    def search_by_ingredient(self):
        """Find all recipes containing a specific ingredient"""
        print("\n🔍 SEARCH RECIPES BY INGREDIENT")
        print("-" * 35)
        
        if not self.recipes:
            print("📚 No recipes available. Add some recipes first!")
            return
        
        search_ingredient = input("Enter ingredient to search for: ").strip().lower()
        
        if not search_ingredient:
            print("❌ Please enter a valid ingredient!")
            return
        
        found_recipes = []
        
        # Search through all recipes (case-insensitive, partial matches)
        for recipe_name, recipe_data in self.recipes.items():
            ingredients = recipe_data[0]
            for ingredient in ingredients:
                if search_ingredient in ingredient.lower():
                    found_recipes.append(recipe_name)

                    break
        
        if not found_recipes:
            print(f"❌ No recipes found containing '{search_ingredient}'")
        else:
            print(f"\n✅ Found {len(found_recipes)} recipe(s) containing '{search_ingredient}':")
            print("-" * 50)
            for i, recipe in enumerate(found_recipes, 1):
                print(f"  {i}. {recipe}")
    
    def update_recipe(self):
        """Update an existing recipe"""
        print("\n✏️  UPDATE RECIPE")
        print("-" * 25)
        
        if not self.recipes:
            print("📚 No recipes available. Add some recipes first!")
            return
        
        recipe_name = input("Enter recipe name to update: ").strip()
        
        # Case-insensitive search for recipe
        found_recipe = None
        for name in self.recipes.keys():
            if name.lower() == recipe_name.lower():
                found_recipe = name
                break
        
        if not found_recipe:
            print(f"❌ Recipe '{recipe_name}' not found!")
            return
        
        # Show update options
        print(f"\nUpdating: {found_recipe}")
        print("What would you like to update?")
        print("1. Ingredients")
        print("2. Instructions")
        print("3. Both")
        
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == "1":
            self._update_ingredients(found_recipe)
        elif choice == "2":
            self._update_instructions(found_recipe)
        elif choice == "3":
            self._update_ingredients(found_recipe)
            self._update_instructions(found_recipe)
        else:
            print("❌ Invalid choice!")
    
    def _update_ingredients(self, recipe_name):
        """Helper method to update ingredients"""
        print(f"\nCurrent ingredients for '{recipe_name}':")
        current_ingredients = self.recipes[recipe_name][0]
        for i, ingredient in enumerate(current_ingredients, 1):
            print(f"  {i}. {ingredient}")
        
        new_ingredients_input = input("\nEnter new ingredients (comma-separated): ").strip()
        
        if not new_ingredients_input:
            print("❌ Ingredients cannot be empty!")
            return
        
        new_ingredients = [ingredient.strip() for ingredient in new_ingredients_input.split(",")]
        new_ingredients = [ing for ing in new_ingredients if ing]
        
        if not new_ingredients:
            print("❌ Please provide valid ingredients!")
            return
        
        self.recipes[recipe_name][0] = new_ingredients
        print("✅ Ingredients updated successfully!")
    
    def _update_instructions(self, recipe_name):
        """Helper method to update instructions"""
        print(f"\nCurrent instructions for '{recipe_name}':")
        print(f"  {self.recipes[recipe_name][1]}")
        
        new_instructions = input("\nEnter new instructions: ").strip()
        
        if not new_instructions:
            print("❌ Instructions cannot be empty!")
            return
        
        self.recipes[recipe_name][1] = new_instructions
        print("✅ Instructions updated successfully!")
    
    def delete_recipe(self):
        """Delete a recipe from the book"""
        print("\n🗑  DELETE RECIPE")
        print("-" * 25)
        
        if not self.recipes:
            print("📚 No recipes available to delete!")
            return
        
        recipe_name = input("Enter recipe name to delete: ").strip()
        
        # Case-insensitive search for recipe
        found_recipe = None
        for name in self.recipes.keys():
            if name.lower() == recipe_name.lower():
                found_recipe = name
                break
        
        if not found_recipe:
            print(f"❌ Recipe '{recipe_name}' not found!")
            return
        
        # Confirmation
        print(f"\n⚠️  Are you sure you want to delete '{found_recipe}'?")
        confirmation = input("Type 'yes' to confirm: ").strip().lower()
        
        if confirmation == 'yes':

            del self.recipes[found_recipe]
            print(f"✅ Recipe '{found_recipe}' deleted successfully!")
        else:
            print("❌ Deletion cancelled.")
    
    def list_all_recipes(self):
        """Display all recipe names"""
        print("\n📚 ALL RECIPES")
        print("-" * 20)
        
        if not self.recipes:
            print("No recipes available. Add some recipes first!")
            return
        
        print(f"Total recipes: {len(self.recipes)}")
        print("-" * 30)
        for i, recipe_name in enumerate(self.recipes.keys(), 1):
            print(f"  {i}. {recipe_name}")
    
    def run(self):
        """Main program loop"""
        print("Welcome to your Digital Recipe Book! 🍳")
        
        while True:
            self.display_menu()
            choice = input("\nEnter your choice (1-7): ").strip()
            
            if choice == "1":
                self.add_recipe()
            elif choice == "2":
                self.view_recipe()
            elif choice == "3":
                self.search_by_ingredient()
            elif choice == "4":
                self.update_recipe()
            elif choice == "5":
                self.delete_recipe()
            elif choice == "6":
                self.list_all_recipes()
            elif choice == "7":
                print("\n👋 Thank you for using Digital Recipe Book!")
                print("Happy cooking! 🍽")
                break
            else:
                print("❌ Invalid choice! Please enter a number between 1-7.")
            
            # Pause before showing menu again
            input("\nPress Enter to continue...")


# Example usage and demonstration
if __name__ == "__main__":
    # Create a recipe book instance
    recipe_book = RecipeBook()
    
    # Add some sample recipes for demonstration
    print("Adding sample recipes for demonstration...")
    
    # Sample Recipe 1
    recipe_book.recipes["Chocolate Chip Cookies"] = [
        ["2 cups flour", "1 cup butter", "1 cup brown sugar", "2 eggs", "1 tsp vanilla", "1 cup chocolate chips"],
        "Mix dry ingredients. Cream butter and sugar, add eggs and vanilla. Combine wet and dry ingredients, fold in chocolate chips. Bake at 375°F for 10-12 minutes."
    ]
    
    # Sample Recipe 2
    recipe_book.recipes["Spaghetti Carbonara"] = [
        ["1 lb spaghetti", "6 eggs", "1 cup parmesan cheese", "8 oz pancetta", "black pepper", "salt"],
        "Cook pasta. Fry pancetta until crispy. Whisk eggs with cheese and pepper. Toss hot pasta with pancetta, then quickly mix in egg mixture. Serve immediately."
    ]
    
    # Sample Recipe 3
    recipe_book.recipes["Caesar Salad"] = [
        ["romaine lettuce", "parmesan cheese", "croutons", "caesar dressing", "anchovies", "lemon juice"],
        "Wash and chop lettuce. Toss with caesar dressing. Add parmesan cheese, croutons, and anchovies. Squeeze fresh lemon juice on top."
    ]
    
    print("Sample recipes added! Ready to use the Recipe Book.\n")
    
    # Start the main program
    recipe_book.run()
