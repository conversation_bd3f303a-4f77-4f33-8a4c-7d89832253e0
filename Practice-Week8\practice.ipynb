{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d6fdc253", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: <PERSON><PERSON>\n", "Email: <EMAIL>\n", "Phone: ************\n"]}], "source": ["name = [\"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"]\n", "email = [\"<EMAIL>\", \"<EMAIL>\", \"Sa<PERSON><EMAIL>\"]\n", "phone = [\"************\", \"************\", \"************\"]\n", "\n", "user = input(\"Input your name: \")\n", "\n", "if user in name:\n", "    index = name.index(user)\n", "    print(f\"Name: {name[index]}\")\n", "    print(f\"Email: {email[index]}\")\n", "    print(f\"Phone: {phone[index]}\")\n", "else:\n", "    print(\"User not found in the list.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "76e1d3a8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}