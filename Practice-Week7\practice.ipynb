{"cells": [{"cell_type": "code", "execution_count": 8, "id": "6cf1d0e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Apple'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data = {'a':\"hi\", 'b':123,99:'c','abc':\"Apple\" }\n", "data['a']\n", "data['b']\n", "data[99]\n", "data[\"abc\"]"]}, {"cell_type": "code", "execution_count": 12, "id": "802f2467", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '<PERSON><PERSON>', 'height': 176}\n", "{'name': '<PERSON><PERSON>', 'height': 176, 'weight': 52}\n"]}], "source": ["myLst = ['a','b',1,2]\n", "myLst[3] = 3\n", "\n", "data = {'name':\"<PERSON>a\", 'height':176}\n", "print(data)\n", "data['weight'] = 52\n", "print(data)"]}, {"cell_type": "code", "execution_count": null, "id": "4e4f4852", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b\n", "['b', 'c']\n", "170\n", "60\n"]}], "source": ["data = {'myLst':['a','b','c','d'],'Story':{'w':60, 'h':170}}\n", "print(data['myLst'][1])\n", "print(data['myLst'][1:3])\n", "print(data['Story']['h'])\n", "print(data['Story']['w'])\n"]}, {"cell_type": "code", "execution_count": 28, "id": "c2609f46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '<PERSON><PERSON>', 'age': 20}\n", "{'school': 'BIU', 'faculty': 'IT'}\n", "{'name': '<PERSON><PERSON>', 'age': 20}\n"]}], "source": ["myLst = [['name','<PERSON><PERSON>'],['age',20]]\n", "data1 = dict(myLst)\n", "myTp=(('school',\"BIU\"), ('faculty',\"IT\"), )\n", "data2 = dict(myTp)\n", "data3 = dict(name=\"<PERSON><PERSON>\", age=20)\n", "\n", "print(data1)\n", "print(data2)\n", "print(data3)"]}, {"cell_type": "code", "execution_count": 38, "id": "e7bd118c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Nak your info is Nak: 0862299234\n"]}], "source": ["contacts = {\n", "    \"nak\": {\"e\": \"<EMAIL>\", \"p\": \"0862299234\"},\n", "}\n", "\n", "name = input(\"Enter your name: \").lower()\n", "choice = input(\"Choose e (email) or p (phone): \").lower()\n", "\n", "if name in contacts:\n", "    if choice in contacts[name]:\n", "        print(f\" {name.title()} your info is {name.title()}: {contacts[name][choice]}\")\n", "    else:\n", "        print(\"Invalid choice. Choose 'e' or 'p'.\")\n", "else:\n", "    print(\"Name not found.\")\n"]}, {"cell_type": "code", "execution_count": 46, "id": "f79b8231", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Name: <PERSON><PERSON>\n", "Phone: 0278367663\n", "Email: <EMAIL>\n"]}], "source": ["name = input('Input Name: ').lower()\n", "\n", "contacts = {\n", "    \"usa\": {\n", "        'phone': ['0278367663'],\n", "        'email': {\n", "            1: '<EMAIL>',\n", "        }\n", "    },\n", "    \"bot\": {\n", "        'phone': ['0987656777'],\n", "        'email': {\n", "            1: '<EMAIL>',\n", "        }\n", "    },\n", "     \"sathya\": {\n", "        'phone': ['0987654321'],\n", "        'email': {\n", "            1: '<EMAIL>',\n", "        }\n", "    }\n", "}\n", "\n", "if name in contacts:\n", "    first_phone = contacts[name]['phone'][0]\n", "    first_email = contacts[name]['email'][1]\n", "\n", "    print(f\"\\nName: {name.title()}\")\n", "    print(f\"Phone: {first_phone}\")\n", "    print(f\"Email: {first_email}\")\n", "else:\n", "    print(\"Name not found.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "25a601db", "metadata": {}, "outputs": [], "source": ["\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}