import tkinter as tk
from tkinter import messagebox
from tkinter import ttk
import math

# Global variables for entry widgets
entry_height = None
entry_weight = None
result_label = None
bmi_value_label = None
category_label = None
progress_bar = None
root = None

def validate_input(value):
    """Validate if the input is a positive number"""
    try:
        num = float(value)
        return num > 0
    except ValueError:
        return False

def get_bmi_color(bmi):
    """Get color based on BMI value"""
    if bmi < 16:
        return "#e74c3c", "#fadbd8"  # Red
    elif 16 <= bmi < 18.5:
        return "#f39c12", "#fdeaa7"  # Orange
    elif 18.5 <= bmi < 25:
        return "#27ae60", "#d5f4e6"  # Green
    elif 25 <= bmi < 30:
        return "#f39c12", "#fdeaa7"  # Orange
    elif 30 <= bmi < 35:
        return "#e74c3c", "#fadbd8"  # Red
    elif 35 <= bmi < 40:
        return "#c0392b", "#f1948a"  # Dark Red
    else:
        return "#922b21", "#ec7063"  # Very Dark Red

def animate_progress_bar(target_bmi):
    """Animate progress bar to show B<PERSON> visually"""
    global progress_bar
    if progress_bar:
        # BMI scale: 0-50 (50 is extreme)
        progress_value = min(target_bmi * 2, 100)  # Scale BMI to 0-100

        # Animate the progress bar
        current = 0
        step = progress_value / 20  # 20 steps for smooth animation

        def update_progress():
            nonlocal current
            if current < progress_value:
                current += step
                progress_bar['value'] = current
                root.after(50, update_progress)
            else:
                progress_bar['value'] = progress_value

        progress_bar['value'] = 0
        root.after(100, update_progress)

def calculate_bmi():
    """Calculate BMI and display the result with beautiful animations"""
    global entry_height, entry_weight, bmi_value_label, category_label, progress_bar

    try:
        # Get input values
        height_str = entry_height.get().strip()
        weight_str = entry_weight.get().strip()

        # Validate inputs
        if not height_str or not weight_str:
            show_error("Please enter both height and weight!")
            return

        if not validate_input(height_str) or not validate_input(weight_str):
            show_error("Please enter valid positive numbers!")
            return

        height = float(height_str)
        weight = float(weight_str)

        # Validate reasonable ranges
        if height < 50 or height > 300:
            show_error("Height should be between 50-300 cm!")
            return

        if weight < 10 or weight > 500:
            show_error("Weight should be between 10-500 kg!")
            return

        # Calculate BMI
        bmi = weight / ((height / 100) ** 2)

        # Determine BMI category
        if bmi < 16:
            category = "Severely Underweight"
            advice = "Please consult a healthcare professional"
        elif 16 <= bmi < 18.5:
            category = "Underweight"
            advice = "Consider a balanced diet to gain healthy weight"
        elif 18.5 <= bmi < 25:
            category = "Normal Weight"
            advice = "Great! Maintain your healthy lifestyle"
        elif 25 <= bmi < 30:
            category = "Overweight"
            advice = "Consider regular exercise and balanced diet"
        elif 30 <= bmi < 35:
            category = "Obese Class I"
            advice = "Consult a healthcare professional for guidance"
        elif 35 <= bmi < 40:
            category = "Obese Class II"
            advice = "Medical supervision recommended"
        else:
            category = "Obese Class III"
            advice = "Immediate medical attention recommended"

        # Get colors
        text_color, bg_color = get_bmi_color(bmi)

        # Update BMI value with animation effect
        bmi_value_label.config(text=f"{bmi:.1f}", fg=text_color,
                              font=("Arial", 36, "bold"))

        # Update category
        category_label.config(text=category, fg=text_color,
                             font=("Arial", 14, "bold"))

        # Animate progress bar
        animate_progress_bar(bmi)

        # Show success message with advice
        show_success(f"BMI: {bmi:.2f} - {category}\n\n💡 {advice}")

    except ValueError:
        show_error("Please enter valid numeric values!")
    except ZeroDivisionError:
        show_error("Height cannot be zero!")
    except Exception as e:
        show_error(f"An unexpected error occurred: {str(e)}")

def show_error(message):
    """Show error message with custom styling"""
    messagebox.showerror("❌ Input Error", message)

def show_success(message):
    """Show success message with custom styling"""
    messagebox.showinfo("✅ BMI Calculated", message)

def clear_fields():
    """Clear all input fields and result with animation"""
    global entry_height, entry_weight, bmi_value_label, category_label, progress_bar

    # Clear input fields
    entry_height.delete(0, tk.END)
    entry_weight.delete(0, tk.END)

    # Reset result displays
    bmi_value_label.config(text="--", fg="#7f8c8d", font=("Arial", 36, "bold"))
    category_label.config(text="Enter your details above", fg="#7f8c8d",
                         font=("Arial", 12, "italic"))

    # Reset progress bar
    if progress_bar:
        progress_bar['value'] = 0

    # Focus on height entry
    entry_height.focus_set()

def create_modern_button(parent, text, command, bg_color, hover_color, text_color="white"):
    """Create a modern styled button with hover effects"""
    button = tk.Button(parent, text=text, command=command,
                      bg=bg_color, fg=text_color, font=("Arial", 11, "bold"),
                      relief="flat", bd=0, cursor="hand2", padx=20, pady=10)

    # Hover effects
    def on_enter(_):
        button.config(bg=hover_color)

    def on_leave(_):
        button.config(bg=bg_color)

    button.bind("<Enter>", on_enter)
    button.bind("<Leave>", on_leave)

    return button

def create_modern_entry(parent, placeholder=""):
    """Create a modern styled entry widget"""
    entry_frame = tk.Frame(parent, bg="white", relief="solid", bd=1)
    entry = tk.Entry(entry_frame, font=("Arial", 14), bg="white", fg="#2c3e50",
                    relief="flat", bd=10, justify="center")
    entry.pack(fill="both", expand=True, padx=2, pady=2)

    # Placeholder functionality
    if placeholder:
        entry.insert(0, placeholder)
        entry.config(fg="#95a5a6")

        def on_focus_in(_):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg="#2c3e50")

        def on_focus_out(_):
            if not entry.get():
                entry.insert(0, placeholder)
                entry.config(fg="#95a5a6")

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    return entry_frame, entry

def create_gui():
    """Create and setup the modern GUI"""
    global entry_height, entry_weight, bmi_value_label, category_label, progress_bar, root

    # Create main window
    root = tk.Tk()
    root.title("🏥 Modern BMI Calculator")
    root.geometry("600x900")
    root.resizable(True, True)
    root.minsize(600, 800)

    # Create gradient-like background
    root.configure(bg="#f8f9fa")

    # Main container with padding
    main_container = tk.Frame(root, bg="#f8f9fa")
    main_container.pack(fill="both", expand=True, padx=20, pady=20)

    # Header section with modern design
    header_frame = tk.Frame(main_container, bg="#3498db", height=120)
    header_frame.pack(fill="x", pady=(0, 30))
    header_frame.pack_propagate(False)

    # Title with icon
    title_label = tk.Label(header_frame, text="🏥 BMI Calculator",
                          bg="#3498db", fg="white",
                          font=("Arial", 24, "bold"))
    title_label.pack(pady=20)

    subtitle_label = tk.Label(header_frame, text="Calculate your Body Mass Index",
                             bg="#3498db", fg="#ecf0f1",
                             font=("Arial", 12))
    subtitle_label.pack()

    # Input section with modern cards
    input_section = tk.Frame(main_container, bg="#f8f9fa")
    input_section.pack(fill="x", pady=(0, 20))

    # Height input card
    height_card = tk.Frame(input_section, bg="white", relief="solid", bd=1)
    height_card.pack(fill="x", pady=(0, 15))

    height_header = tk.Frame(height_card, bg="#ecf0f1", height=40)
    height_header.pack(fill="x")
    height_header.pack_propagate(False)

    tk.Label(height_header, text="📏 Height (cm)", bg="#ecf0f1", fg="#2c3e50",
             font=("Arial", 12, "bold")).pack(pady=10)

    height_entry_frame, entry_height = create_modern_entry(height_card, "Enter your height")
    height_entry_frame.pack(fill="x", padx=15, pady=15)

    # Weight input card
    weight_card = tk.Frame(input_section, bg="white", relief="solid", bd=1)
    weight_card.pack(fill="x", pady=(0, 15))

    weight_header = tk.Frame(weight_card, bg="#ecf0f1", height=40)
    weight_header.pack(fill="x")
    weight_header.pack_propagate(False)

    tk.Label(weight_header, text="⚖️ Weight (kg)", bg="#ecf0f1", fg="#2c3e50",
             font=("Arial", 12, "bold")).pack(pady=10)

    weight_entry_frame, entry_weight = create_modern_entry(weight_card, "Enter your weight")
    weight_entry_frame.pack(fill="x", padx=15, pady=15)

    # Button section
    button_section = tk.Frame(main_container, bg="#f8f9fa")
    button_section.pack(fill="x", pady=(0, 20))

    button_container = tk.Frame(button_section, bg="#f8f9fa")
    button_container.pack()

    # Modern buttons
    calculate_btn = create_modern_button(button_container, "🧮 Calculate BMI",
                                       calculate_bmi, "#27ae60", "#2ecc71")
    calculate_btn.pack(side="left", padx=(0, 10))

    clear_btn = create_modern_button(button_container, "🗑️ Clear",
                                   clear_fields, "#e74c3c", "#c0392b")
    clear_btn.pack(side="left")

    # Result section with modern design
    result_section = tk.Frame(main_container, bg="white", relief="solid", bd=1)
    result_section.pack(fill="x", pady=(0, 20))

    result_header = tk.Frame(result_section, bg="#34495e", height=50)
    result_header.pack(fill="x")
    result_header.pack_propagate(False)

    tk.Label(result_header, text="📊 Your BMI Result", bg="#34495e", fg="white",
             font=("Arial", 14, "bold")).pack(pady=12)

    # BMI display area
    bmi_display = tk.Frame(result_section, bg="white")
    bmi_display.pack(fill="x", padx=20, pady=20)

    bmi_value_label = tk.Label(bmi_display, text="--", bg="white", fg="#7f8c8d",
                              font=("Arial", 48, "bold"))
    bmi_value_label.pack()

    category_label = tk.Label(bmi_display, text="Enter your details above",
                             bg="white", fg="#7f8c8d", font=("Arial", 14, "italic"))
    category_label.pack(pady=(5, 15))

    # Progress bar for visual BMI representation
    progress_frame = tk.Frame(result_section, bg="white")
    progress_frame.pack(fill="x", padx=20, pady=(0, 20))

    tk.Label(progress_frame, text="BMI Scale", bg="white", fg="#7f8c8d",
             font=("Arial", 10, "bold")).pack()

    progress_bar = ttk.Progressbar(progress_frame, length=400,
                                  mode='determinate', style="Custom.Horizontal.TProgressbar")
    progress_bar.pack(pady=10)

    # BMI categories info with detailed table
    info_section = tk.Frame(main_container, bg="white", relief="solid", bd=1)
    info_section.pack(fill="x")

    info_header = tk.Frame(info_section, bg="#9b59b6", height=40)
    info_header.pack(fill="x")
    info_header.pack_propagate(False)

    tk.Label(info_header, text="� Detailed BMI Information Table", bg="#9b59b6", fg="white",
             font=("Arial", 12, "bold")).pack(pady=8)

    # Create table frame with scrollbar
    table_container = tk.Frame(info_section, bg="white")
    table_container.pack(fill="both", expand=True, padx=10, pady=10)

    # Table data
    bmi_data = [
        ["Category", "BMI Range", "Health Risk", "Recommendations", "Color Code"],
        ["Severely Underweight", "< 16.0", "Very High", "Immediate medical consultation required", "🔴"],
        ["Underweight", "16.0 - 18.4", "High", "Increase caloric intake, consult nutritionist", "🟠"],
        ["Normal Weight", "18.5 - 24.9", "Low", "Maintain current lifestyle and diet", "🟢"],
        ["Overweight", "25.0 - 29.9", "Moderate", "Regular exercise, balanced diet", "🟡"],
        ["Obese Class I", "30.0 - 34.9", "High", "Weight loss program, medical supervision", "🔴"],
        ["Obese Class II", "35.0 - 39.9", "Very High", "Intensive weight management, medical care", "🔴"],
        ["Obese Class III", "≥ 40.0", "Extremely High", "Immediate medical intervention required", "⚫"]
    ]

    # Create table headers
    header_frame = tk.Frame(table_container, bg="#34495e")
    header_frame.pack(fill="x", pady=(0, 2))

    # Column widths (proportional)
    col_widths = [0.18, 0.15, 0.15, 0.42, 0.10]

    for i, header in enumerate(bmi_data[0]):
        header_label = tk.Label(header_frame, text=header, bg="#34495e", fg="white",
                               font=("Arial", 10, "bold"), relief="solid", bd=1)
        header_label.place(relx=sum(col_widths[:i]), rely=0,
                          relwidth=col_widths[i], relheight=1)

    # Create table rows
    for row_idx, row_data in enumerate(bmi_data[1:], 1):
        # Alternate row colors
        row_bg = "#f8f9fa" if row_idx % 2 == 0 else "white"

        row_frame = tk.Frame(table_container, bg=row_bg, height=35)
        row_frame.pack(fill="x", pady=1)
        row_frame.pack_propagate(False)

        for col_idx, cell_data in enumerate(row_data):
            # Special styling for different columns
            if col_idx == 0:  # Category column
                font_style = ("Arial", 9, "bold")
                text_color = "#2c3e50"
            elif col_idx == 1:  # BMI Range column
                font_style = ("Arial", 9, "bold")
                text_color = "#3498db"
            elif col_idx == 2:  # Health Risk column
                font_style = ("Arial", 9, "bold")
                if "Very High" in cell_data or "Extremely High" in cell_data:
                    text_color = "#e74c3c"
                elif "High" in cell_data:
                    text_color = "#f39c12"
                elif "Moderate" in cell_data:
                    text_color = "#f1c40f"
                else:
                    text_color = "#27ae60"
            elif col_idx == 3:  # Recommendations column
                font_style = ("Arial", 8)
                text_color = "#7f8c8d"
            else:  # Color Code column
                font_style = ("Arial", 12)
                text_color = "#2c3e50"

            cell_label = tk.Label(row_frame, text=cell_data, bg=row_bg, fg=text_color,
                                 font=font_style, relief="solid", bd=1, wraplength=200)
            cell_label.place(relx=sum(col_widths[:col_idx]), rely=0,
                           relwidth=col_widths[col_idx], relheight=1)

    # Add additional information section
    additional_info_frame = tk.Frame(info_section, bg="#ecf0f1")
    additional_info_frame.pack(fill="x", padx=10, pady=(10, 0))

    info_title = tk.Label(additional_info_frame, text="📝 Important Notes:",
                         bg="#ecf0f1", fg="#2c3e50", font=("Arial", 10, "bold"))
    info_title.pack(anchor="w", padx=10, pady=(10, 5))

    notes_text = """• BMI is a screening tool and not a diagnostic measure
• Results may vary for athletes, elderly, and pregnant women
• Consult healthcare professionals for personalized advice
• BMI doesn't distinguish between muscle and fat mass
• Consider waist circumference and other health factors"""

    notes_label = tk.Label(additional_info_frame, text=notes_text, bg="#ecf0f1",
                          fg="#7f8c8d", font=("Arial", 9), justify="left")
    notes_label.pack(anchor="w", padx=20, pady=(0, 10))

    # Add footer with calculation formula
    formula_frame = tk.Frame(info_section, bg="#2c3e50")
    formula_frame.pack(fill="x")

    formula_text = "📐 BMI Formula: Weight (kg) ÷ [Height (m)]² | Example: 70kg ÷ (1.75m)² = 22.9"
    tk.Label(formula_frame, text=formula_text, bg="#2c3e50", fg="white",
             font=("Arial", 9, "italic")).pack(pady=8)

    # Configure ttk style for progress bar
    style = ttk.Style()
    style.configure("Custom.Horizontal.TProgressbar",
                   background="#3498db", troughcolor="#ecf0f1")

    # Bind Enter key to calculate
    root.bind('<Return>', lambda _: calculate_bmi())

    # Focus on height entry initially
    entry_height.focus_set()

    return root

def main():
    """Main function to run the BMI calculator"""
    root = create_gui()
    root.mainloop()

if __name__ == "__main__":
    main()