# Test script to verify Save button functionality

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service

def test_save_button_display():
    """Test if Save button appears correctly in dialogs"""
    print("Testing Save button display...")
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return False
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return False
    
    try:
        # Create main window
        root = tk.Tk()
        root.title("Save Button Test")
        root.geometry("300x200")
        
        def test_user_dialog():
            """Test UserDialog Save button"""
            try:
                from dialogs import UserDialog
                dialog = UserDialog(root, "Test User Dialog")
                print("✅ UserDialog created - Check if Save button is visible")
            except Exception as e:
                print(f"❌ UserDialog error: {e}")
        
        def test_product_dialog():
            """Test ProductDialog Save button"""
            try:
                from dialogs import ProductDialog
                dialog = ProductDialog(root, "Test Product Dialog")
                print("✅ ProductDialog created - Check if Save button is visible")
            except Exception as e:
                print(f"❌ ProductDialog error: {e}")
        
        def test_category_dialog():
            """Test CategoryDialog Save button"""
            try:
                from dialogs import CategoryDialog
                dialog = CategoryDialog(root, "Test Category Dialog")
                print("✅ CategoryDialog created - Check if Save button is visible")
            except Exception as e:
                print(f"❌ CategoryDialog error: {e}")
        
        # Create test buttons
        ttk.Label(root, text="Click buttons to test Save button display:", 
                 font=('Arial', 12)).pack(pady=20)
        
        ttk.Button(root, text="Test User Dialog", 
                  command=test_user_dialog).pack(pady=5)
        
        ttk.Button(root, text="Test Product Dialog", 
                  command=test_product_dialog).pack(pady=5)
        
        ttk.Button(root, text="Test Category Dialog", 
                  command=test_category_dialog).pack(pady=5)
        
        ttk.Button(root, text="Close Test", 
                  command=root.destroy).pack(pady=10)
        
        print("✅ Test window created")
        print("📋 Instructions:")
        print("1. Click the test buttons to open dialogs")
        print("2. Check if 'Save' button appears at the bottom right")
        print("3. Check if 'Cancel' button appears next to Save")
        print("4. Close dialogs and test window when done")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ Test window error: {e}")
        return False
    finally:
        if db.connection:
            db.disconnect()

def create_simple_dialog_test():
    """Create a simple dialog to test button layout"""
    print("\nCreating simple dialog test...")
    
    class SimpleTestDialog:
        def __init__(self, parent):
            self.result = None
            
            # Create dialog window
            self.dialog = tk.Toplevel(parent)
            self.dialog.title("Simple Save Button Test")
            self.dialog.geometry("400x300")
            self.dialog.transient(parent)
            self.dialog.grab_set()
            
            # Create main frame
            self.main_frame = ttk.Frame(self.dialog, padding=20)
            self.main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Add some content
            ttk.Label(self.main_frame, text="Test Dialog Content", 
                     font=('Arial', 14)).pack(pady=20)
            
            ttk.Label(self.main_frame, text="Name:").pack(anchor='w')
            self.name_var = tk.StringVar()
            ttk.Entry(self.main_frame, textvariable=self.name_var, width=30).pack(pady=5)
            
            ttk.Label(self.main_frame, text="Email:").pack(anchor='w')
            self.email_var = tk.StringVar()
            ttk.Entry(self.main_frame, textvariable=self.email_var, width=30).pack(pady=5)
            
            # Create buttons frame
            button_frame = ttk.Frame(self.main_frame)
            button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
            
            # Create buttons
            ttk.Button(button_frame, text="Cancel", 
                      command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="Save", 
                      command=self.save).pack(side=tk.RIGHT)
            
            print("✅ Simple dialog created with Save and Cancel buttons")
        
        def save(self):
            """Handle Save button"""
            name = self.name_var.get().strip()
            email = self.email_var.get().strip()
            
            if not name:
                messagebox.showerror("Error", "Name is required")
                return
            
            if not email:
                messagebox.showerror("Error", "Email is required")
                return
            
            self.result = {'name': name, 'email': email}
            messagebox.showinfo("Success", f"Saved: {name} ({email})")
            self.dialog.destroy()
        
        def cancel(self):
            """Handle Cancel button"""
            self.result = None
            self.dialog.destroy()
    
    # Create test window
    root = tk.Tk()
    root.title("Dialog Button Test")
    root.geometry("300x150")
    
    def open_simple_dialog():
        dialog = SimpleTestDialog(root)
    
    ttk.Label(root, text="Simple Dialog Button Test", 
             font=('Arial', 14)).pack(pady=20)
    
    ttk.Button(root, text="Open Test Dialog", 
              command=open_simple_dialog).pack(pady=10)
    
    ttk.Button(root, text="Close", 
              command=root.destroy).pack(pady=5)
    
    print("✅ Simple test window created")
    print("📋 Click 'Open Test Dialog' to see Save/Cancel buttons")
    
    root.mainloop()

if __name__ == "__main__":
    print("=" * 60)
    print("SAVE BUTTON DISPLAY TEST")
    print("=" * 60)
    
    choice = input("Choose test:\n1. Test with actual dialogs\n2. Test with simple dialog\nEnter choice (1 or 2): ")
    
    if choice == "1":
        test_save_button_display()
    elif choice == "2":
        create_simple_dialog_test()
    else:
        print("Invalid choice. Running simple dialog test...")
        create_simple_dialog_test()
