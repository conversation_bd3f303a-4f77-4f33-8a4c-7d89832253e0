# Report Management GUI for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from report_management import report_service
from product_management import product_service
from user_management import user_service
from config import GUI_CONFIG
from datetime import datetime, timedelta
import json

# Optional matplotlib for charts
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class ReportManagementFrame:
    def __init__(self, parent):
        self.parent = parent
        self.frame = ttk.Frame(parent)
        
        # Create report management interface
        self.create_interface()
    
    def create_interface(self):
        """Create the report management interface"""
        # Title and controls
        self.create_header()
        
        # Main content area with notebook
        self.create_notebook()
        
        # Report generation tab
        self.create_report_generation_tab()
        
        # Report viewer tab
        self.create_report_viewer_tab()
        
        # Report history tab
        self.create_report_history_tab()
    
    def create_header(self):
        """Create header with title and main controls"""
        header_frame = ttk.Frame(self.frame)
        header_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                         pady=GUI_CONFIG['padding']['medium'])
        
        # Title
        ttk.Label(header_frame, text="Report Management", 
                 font=GUI_CONFIG['fonts']['title']).pack(side=tk.LEFT)
        
        # Quick action buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT)
        
        ttk.Button(button_frame, text="Low Stock Alert", 
                  command=self.quick_low_stock_report).pack(side=tk.LEFT, 
                                                           padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(button_frame, text="Inventory Summary", 
                  command=self.quick_inventory_report).pack(side=tk.LEFT, 
                                                           padx=GUI_CONFIG['padding']['small'])
    
    def create_notebook(self):
        """Create notebook for different tabs"""
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, 
                          padx=GUI_CONFIG['padding']['medium'], 
                          pady=GUI_CONFIG['padding']['small'])
    
    def create_report_generation_tab(self):
        """Create report generation tab"""
        self.generation_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.generation_tab, text="Generate Reports")
        
        # Low Stock Report Section
        low_stock_frame = ttk.LabelFrame(self.generation_tab, text="Low Stock Report", 
                                        padding=GUI_CONFIG['padding']['medium'])
        low_stock_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                            pady=GUI_CONFIG['padding']['small'])
        
        ttk.Label(low_stock_frame, text="Stock Threshold:").grid(row=0, column=0, sticky='w')
        self.low_stock_threshold_var = tk.StringVar(value="10")
        ttk.Entry(low_stock_frame, textvariable=self.low_stock_threshold_var, 
                 width=10).grid(row=0, column=1, padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(low_stock_frame, text="Generate Low Stock Report", 
                  command=self.generate_low_stock_report).grid(row=0, column=2, 
                                                              padx=GUI_CONFIG['padding']['small'])
        
        # Inventory Report Section
        inventory_frame = ttk.LabelFrame(self.generation_tab, text="Inventory Report", 
                                        padding=GUI_CONFIG['padding']['medium'])
        inventory_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                            pady=GUI_CONFIG['padding']['small'])
        
        ttk.Label(inventory_frame, text="Category Filter:").grid(row=0, column=0, sticky='w')
        self.inventory_category_var = tk.StringVar()
        self.inventory_category_combo = ttk.Combobox(inventory_frame, 
                                                    textvariable=self.inventory_category_var, 
                                                    state='readonly', width=25)
        self.inventory_category_combo.grid(row=0, column=1, padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(inventory_frame, text="Generate Inventory Report", 
                  command=self.generate_inventory_report).grid(row=0, column=2, 
                                                              padx=GUI_CONFIG['padding']['small'])
        
        # Load categories
        self.load_categories_for_reports()
        
        # Sales Report Section
        sales_frame = ttk.LabelFrame(self.generation_tab, text="Sales Report", 
                                    padding=GUI_CONFIG['padding']['medium'])
        sales_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                        pady=GUI_CONFIG['padding']['small'])
        
        # Date range selection
        ttk.Label(sales_frame, text="Date Range:").grid(row=0, column=0, sticky='w')
        
        date_frame = ttk.Frame(sales_frame)
        date_frame.grid(row=0, column=1, padx=GUI_CONFIG['padding']['small'])
        
        self.sales_period_var = tk.StringVar(value="Last 30 Days")
        period_combo = ttk.Combobox(date_frame, textvariable=self.sales_period_var, 
                                   state='readonly', width=15)
        period_combo['values'] = ["Last 7 Days", "Last 30 Days", "Last 90 Days", "Custom Range"]
        period_combo.pack(side=tk.LEFT)
        
        ttk.Button(sales_frame, text="Generate Sales Report", 
                  command=self.generate_sales_report).grid(row=0, column=2, 
                                                          padx=GUI_CONFIG['padding']['small'])
        
        # User Activity Report Section
        activity_frame = ttk.LabelFrame(self.generation_tab, text="User Activity Report", 
                                       padding=GUI_CONFIG['padding']['medium'])
        activity_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                           pady=GUI_CONFIG['padding']['small'])
        
        ttk.Label(activity_frame, text="User Filter:").grid(row=0, column=0, sticky='w')
        self.activity_user_var = tk.StringVar()
        self.activity_user_combo = ttk.Combobox(activity_frame, 
                                               textvariable=self.activity_user_var, 
                                               state='readonly', width=25)
        self.activity_user_combo.grid(row=0, column=1, padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(activity_frame, text="Generate Activity Report", 
                  command=self.generate_activity_report).grid(row=0, column=2, 
                                                             padx=GUI_CONFIG['padding']['small'])
        
        # Load users
        self.load_users_for_reports()
    
    def create_report_viewer_tab(self):
        """Create report viewer tab"""
        self.viewer_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.viewer_tab, text="View Report")
        
        # Report display area
        self.report_text = tk.Text(self.viewer_tab, wrap=tk.WORD, font=('Courier', 10))
        
        # Scrollbars for text widget
        v_scrollbar = ttk.Scrollbar(self.viewer_tab, orient=tk.VERTICAL, command=self.report_text.yview)
        h_scrollbar = ttk.Scrollbar(self.viewer_tab, orient=tk.HORIZONTAL, command=self.report_text.xview)
        self.report_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack text widget and scrollbars
        self.report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, 
                             padx=GUI_CONFIG['padding']['medium'], 
                             pady=GUI_CONFIG['padding']['medium'])
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=GUI_CONFIG['padding']['medium'])
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X, padx=GUI_CONFIG['padding']['medium'])
        
        # Report actions frame
        actions_frame = ttk.Frame(self.viewer_tab)
        actions_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                          pady=GUI_CONFIG['padding']['small'])
        
        ttk.Button(actions_frame, text="Export to File", 
                  command=self.export_report).pack(side=tk.LEFT, 
                                                  padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(actions_frame, text="Print Report", 
                  command=self.print_report).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(actions_frame, text="Clear", 
                  command=self.clear_report).pack(side=tk.LEFT, 
                                                 padx=GUI_CONFIG['padding']['small'])
    
    def create_report_history_tab(self):
        """Create report history tab"""
        self.history_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.history_tab, text="Report History")
        
        # History list frame
        history_frame = ttk.Frame(self.history_tab)
        history_frame.pack(fill=tk.BOTH, expand=True, 
                          padx=GUI_CONFIG['padding']['medium'], 
                          pady=GUI_CONFIG['padding']['medium'])
        
        # Create treeview for report history
        columns = ('ID', 'Type', 'Generated By', 'Date', 'Status')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Type': 150, 'Generated By': 150, 'Date': 150, 'Status': 100}
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=column_widths.get(col, 100))
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # History actions frame
        history_actions_frame = ttk.Frame(self.history_tab)
        history_actions_frame.pack(fill=tk.X, padx=GUI_CONFIG['padding']['medium'], 
                                  pady=GUI_CONFIG['padding']['small'])
        
        ttk.Button(history_actions_frame, text="View Report", 
                  command=self.view_historical_report).pack(side=tk.LEFT, 
                                                           padx=GUI_CONFIG['padding']['small'])
        
        ttk.Button(history_actions_frame, text="Refresh History", 
                  command=self.load_report_history).pack(side=tk.LEFT, 
                                                        padx=GUI_CONFIG['padding']['small'])
        
        # Load initial history
        self.load_report_history()
    
    def load_categories_for_reports(self):
        """Load categories for report filtering"""
        try:
            categories = product_service.get_all_categories()
            category_names = ["All Categories"] + [cat.name for cat in categories]
            self.inventory_category_combo['values'] = category_names
            self.inventory_category_var.set("All Categories")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load categories: {str(e)}")

    def load_users_for_reports(self):
        """Load users for activity report filtering"""
        try:
            users = user_service.get_all_users()
            user_names = ["All Users"] + [f"{user.full_name} ({user.username})" for user in users]
            self.activity_user_combo['values'] = user_names
            self.activity_user_var.set("All Users")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load users: {str(e)}")

    def quick_low_stock_report(self):
        """Generate quick low stock report"""
        self.generate_low_stock_report()

    def quick_inventory_report(self):
        """Generate quick inventory report"""
        self.generate_inventory_report()

    def generate_low_stock_report(self):
        """Generate low stock report"""
        try:
            threshold = int(self.low_stock_threshold_var.get())
            report_data = report_service.generate_low_stock_report(threshold)

            self.display_report("Low Stock Report", report_data)
            self.notebook.select(self.viewer_tab)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid threshold number")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def generate_inventory_report(self):
        """Generate inventory report"""
        try:
            category_filter = self.inventory_category_var.get()
            category_id = None

            if category_filter != "All Categories":
                categories = product_service.get_all_categories()
                for cat in categories:
                    if cat.name == category_filter:
                        category_id = cat.id
                        break

            report_data = report_service.generate_inventory_report(category_id)

            self.display_report("Inventory Report", report_data)
            self.notebook.select(self.viewer_tab)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def generate_sales_report(self):
        """Generate sales report"""
        try:
            period = self.sales_period_var.get()

            # Calculate date range based on selection
            end_date = datetime.now()
            if period == "Last 7 Days":
                start_date = end_date - timedelta(days=7)
            elif period == "Last 30 Days":
                start_date = end_date - timedelta(days=30)
            elif period == "Last 90 Days":
                start_date = end_date - timedelta(days=90)
            else:  # Custom Range - for now use last 30 days
                start_date = end_date - timedelta(days=30)

            report_data = report_service.generate_sales_report(start_date, end_date)

            self.display_report("Sales Report", report_data)
            self.notebook.select(self.viewer_tab)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def generate_activity_report(self):
        """Generate user activity report"""
        try:
            user_filter = self.activity_user_var.get()
            user_id = None

            if user_filter != "All Users":
                users = user_service.get_all_users()
                for user in users:
                    if f"{user.full_name} ({user.username})" == user_filter:
                        user_id = user.id
                        break

            # Use last 30 days for activity report
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

            report_data = report_service.generate_user_activity_report(user_id, start_date, end_date)

            self.display_report("User Activity Report", report_data)
            self.notebook.select(self.viewer_tab)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

    def display_report(self, title: str, report_data: dict):
        """Display report in the viewer"""
        self.report_text.delete('1.0', tk.END)

        # Format report for display
        report_content = self.format_report_for_display(title, report_data)

        self.report_text.insert('1.0', report_content)
        self.current_report_data = report_data
        self.current_report_title = title

    def format_report_for_display(self, title: str, report_data: dict) -> str:
        """Format report data for text display"""
        content = []
        content.append("=" * 80)
        content.append(f"{title.upper()}")
        content.append("=" * 80)
        content.append(f"Generated: {report_data.get('generated_at', 'Unknown')}")
        content.append("")

        if report_data['report_type'] == 'low_stock':
            content.append(f"Stock Threshold: {report_data.get('threshold', 'N/A')}")
            content.append(f"Total Products Below Threshold: {report_data.get('total_products', 0)}")
            content.append("")

            if report_data.get('products'):
                content.append("LOW STOCK PRODUCTS:")
                content.append("-" * 40)
                for product in report_data['products']:
                    content.append(f"• {product['name']} ({product['sku']})")
                    content.append(f"  Current Stock: {product['current_quantity']}")
                    content.append(f"  Min Level: {product['min_stock_level']}")
                    content.append(f"  Category: {product['category']}")
                    content.append(f"  Status: {product['status']}")
                    content.append("")
            else:
                content.append("No products below the specified threshold.")

        elif report_data['report_type'] == 'inventory':
            summary = report_data.get('summary', {})
            content.append("INVENTORY SUMMARY:")
            content.append("-" * 40)
            content.append(f"Total Products: {summary.get('total_products', 0)}")
            content.append(f"Total Items: {summary.get('total_items', 0)}")
            content.append(f"Total Value: ${summary.get('total_value', 0):,.2f}")
            content.append(f"Low Stock Items: {summary.get('low_stock_items', 0)}")
            content.append(f"Out of Stock Items: {summary.get('out_of_stock_items', 0)}")
            content.append("")

            if report_data.get('products'):
                content.append("PRODUCT DETAILS:")
                content.append("-" * 40)
                for product in report_data['products'][:20]:  # Show first 20 products
                    content.append(f"• {product['name']} ({product['sku']})")
                    content.append(f"  Quantity: {product['quantity']} | Price: ${product['price']:.2f}")
                    content.append(f"  Value: ${product['total_value']:.2f} | Status: {product['status']}")
                    content.append("")

                if len(report_data['products']) > 20:
                    content.append(f"... and {len(report_data['products']) - 20} more products")

        elif report_data['report_type'] == 'sales':
            summary = report_data.get('summary', {})
            period = report_data.get('period', {})
            content.append("SALES SUMMARY:")
            content.append("-" * 40)
            content.append(f"Period: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            content.append(f"Total Transactions: {summary.get('total_transactions', 0)}")
            content.append(f"Total Quantity Sold: {summary.get('total_quantity_sold', 0)}")
            content.append(f"Total Revenue: ${summary.get('total_revenue', 0):,.2f}")
            content.append(f"Average Transaction Value: ${summary.get('average_transaction_value', 0):,.2f}")
            content.append("")

            if report_data.get('product_summary'):
                content.append("TOP SELLING PRODUCTS:")
                content.append("-" * 40)
                sorted_products = sorted(report_data['product_summary'].items(),
                                       key=lambda x: x[1]['quantity_sold'], reverse=True)
                for product_name, data in sorted_products[:10]:
                    content.append(f"• {product_name}")
                    content.append(f"  Quantity Sold: {data['quantity_sold']}")
                    content.append(f"  Revenue: ${data['revenue']:,.2f}")
                    content.append("")

        elif report_data['report_type'] == 'user_activity':
            summary = report_data.get('summary', {})
            period = report_data.get('period', {})
            content.append("USER ACTIVITY SUMMARY:")
            content.append("-" * 40)
            content.append(f"Period: {period.get('start_date', 'N/A')} to {period.get('end_date', 'N/A')}")
            content.append(f"Total Transactions: {summary.get('total_transactions', 0)}")
            content.append(f"Active Users: {summary.get('users_active', 0)}")
            content.append("")

            if report_data.get('user_summary'):
                content.append("USER ACTIVITY DETAILS:")
                content.append("-" * 40)
                for user_name, data in report_data['user_summary'].items():
                    content.append(f"• {user_name} ({data['role']})")
                    content.append(f"  Transactions: {data['transaction_count']}")
                    content.append(f"  Stock In: {data['stock_in']} | Stock Out: {data['stock_out']}")
                    content.append("")

        return "\n".join(content)

    def export_report(self):
        """Export current report to file"""
        if not hasattr(self, 'current_report_data'):
            messagebox.showwarning("Warning", "No report to export")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                if filename.endswith('.json'):
                    with open(filename, 'w') as f:
                        json.dump(self.current_report_data, f, indent=2, default=str)
                else:
                    with open(filename, 'w') as f:
                        f.write(self.report_text.get('1.0', tk.END))

                messagebox.showinfo("Success", f"Report exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report: {str(e)}")

    def print_report(self):
        """Print current report"""
        messagebox.showinfo("Print", "Print functionality would be implemented here.\nFor now, you can export the report and print from your text editor.")

    def clear_report(self):
        """Clear the report viewer"""
        self.report_text.delete('1.0', tk.END)
        if hasattr(self, 'current_report_data'):
            delattr(self, 'current_report_data')
        if hasattr(self, 'current_report_title'):
            delattr(self, 'current_report_title')

    def load_report_history(self):
        """Load report history"""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        try:
            reports = report_service.get_recent_reports(50)

            for report in reports:
                # Format report type
                report_type = report['report_type'].replace('_', ' ').title()

                # Format date
                date_str = report['created_at'][:19] if report['created_at'] else "Unknown"

                self.history_tree.insert('', 'end', values=(
                    report['id'],
                    report_type,
                    report['generated_by'],
                    date_str,
                    "Available"
                ))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load report history: {str(e)}")

    def view_historical_report(self):
        """View selected historical report"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a report to view")
            return

        item = self.history_tree.item(selection[0])
        report_id = item['values'][0]
        report_type = item['values'][1]

        # For now, show a placeholder message
        messagebox.showinfo("Historical Report",
                           f"Viewing historical report #{report_id}\nType: {report_type}\n\nFull historical report viewing would be implemented here.")

    def get_frame(self):
        """Get the main frame"""
        return self.frame
