# Debug script for CategoryDialog data capture

import tkinter as tk
from tkinter import ttk, messagebox
from database import db
from user_management import user_service

def debug_category_dialog():
    """Debug CategoryDialog specifically"""
    print("=" * 60)
    print("DEBUGGING CATEGORY DIALOG DATA CAPTURE")
    print("=" * 60)
    
    # Initialize database and authenticate
    if not db.connect():
        print("❌ Database connection failed")
        return
    
    db.create_tables()
    
    if not user_service.authenticate('admin', 'admin123'):
        print("❌ Authentication failed")
        return
    
    print("✅ Database initialized and authenticated")
    
    # Create main window
    root = tk.Tk()
    root.title("Category Dialog Debug")
    root.geometry("500x300")
    
    # Instructions
    instructions = tk.Text(root, height=8, width=60)
    instructions.pack(pady=10)
    instructions.insert('1.0', """CATEGORY DIALOG DEBUG TEST

Instructions:
1. Click 'Open Category Dialog' button below
2. Enter category name: 'Debug Test Category'
3. Enter description: 'This is a debug test'
4. Click Save button
5. Check console output for debug messages
6. Check result display below

Expected debug output:
- Save button clicked
- Validation messages
- Data capture messages
- Result information
""")
    instructions.config(state='disabled')
    
    # Result display
    result_label = ttk.Label(root, text="Result will appear here", 
                            font=('Arial', 12), foreground='blue')
    result_label.pack(pady=10)
    
    def test_category_dialog():
        """Test CategoryDialog with debugging"""
        print("\n" + "="*50)
        print("OPENING CATEGORY DIALOG FOR DEBUG TEST")
        print("="*50)
        
        try:
            from dialogs import CategoryDialog
            print("✅ CategoryDialog imported successfully")
            
            dialog = CategoryDialog(root, "Debug Category Dialog")
            print("✅ CategoryDialog created successfully")
            
            # Wait for dialog to close
            print("⏳ Waiting for user to interact with dialog...")
            root.wait_window(dialog.dialog)
            print("✅ Dialog closed")
            
            # Check result
            if hasattr(dialog, 'result'):
                print(f"✅ Dialog has result attribute: {dialog.result}")
                if dialog.result:
                    print("✅ Result is not None/empty")
                    result_text = f"SUCCESS!\nName: {dialog.result.get('name', 'N/A')}\nDescription: {dialog.result.get('description', 'N/A')}"
                    result_label.config(text=result_text, foreground='green')
                    
                    # Try to actually create the category
                    try:
                        from product_management import product_service
                        success = product_service.create_category(
                            name=dialog.result['name'],
                            description=dialog.result['description']
                        )
                        if success:
                            print("✅ Category created successfully in database!")
                            result_label.config(text=result_text + "\n\n✅ SAVED TO DATABASE!", 
                                              foreground='green')
                        else:
                            print("❌ Failed to create category in database")
                            result_label.config(text=result_text + "\n\n❌ Database save failed", 
                                              foreground='orange')
                    except Exception as e:
                        print(f"❌ Database error: {e}")
                        result_label.config(text=result_text + f"\n\n❌ Database error: {e}", 
                                          foreground='orange')
                else:
                    print("❌ Result is None or empty")
                    result_label.config(text="❌ No data captured (result is None)", 
                                      foreground='red')
            else:
                print("❌ Dialog has no result attribute")
                result_label.config(text="❌ Dialog has no result attribute", 
                                  foreground='red')
            
        except Exception as e:
            print(f"❌ Error testing CategoryDialog: {e}")
            import traceback
            traceback.print_exc()
            result_label.config(text=f"❌ Error: {e}", foreground='red')
        
        print("="*50)
        print("DEBUG TEST COMPLETED")
        print("="*50)
    
    # Test button
    ttk.Button(root, text="Open Category Dialog", 
              command=test_category_dialog).pack(pady=10)
    
    ttk.Button(root, text="Close", 
              command=root.destroy).pack(pady=5)
    
    print("✅ Debug window opened")
    print("📋 Click 'Open Category Dialog' to start debugging")
    print("📋 Watch console output for debug messages")
    
    root.mainloop()
    
    # Clean up
    if db.connection:
        db.disconnect()

if __name__ == "__main__":
    debug_category_dialog()
