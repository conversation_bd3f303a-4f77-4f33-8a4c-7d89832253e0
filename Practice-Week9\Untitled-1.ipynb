{"cells": [{"cell_type": "code", "execution_count": 2, "id": "7d226a6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n", "6\n", "5\n", "5\n"]}], "source": ["myLst = ['pineapple', 'orange', 'mango', 'grape']\n", "\n", "index = 0\n", "while index < len(myLst):\n", "    if myLst[index] :\n", "        element = myLst[index]\n", "        print(len(element))\n", "    index += 1"]}, {"cell_type": "code", "execution_count": null, "id": "31323600", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "3\n", "5\n", "7\n", "9\n"]}], "source": ["lst = [1, 3, 5, 7, 9]\n", "interator = iter(lst)\n", "\n", "try:\n", "    while True: \n", "        element = next(interator)\n", "        print(element)\n", "except StopIteration:\n", "    pass"]}, {"cell_type": "code", "execution_count": 14, "id": "f1077db6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["milk:           15.00\n", "bread:          10.00\n", "eggs:           4.00\n", "cheese:         45.00\n", "cookies:        8.00\n", "juice:          14.40\n", "yogurt:         32.40\n", "apples:         62.50\n", "bananas:        39.00\n", "oranges:        48.00\n", "grapes:         54.00\n", "------------------------------\n", "Total value of the supermarket: 332.30\n"]}], "source": ["supermarket = {\"milk\":{\"quantity\":10, \"price\":1.5},\n", "              \"bread\":{\"quantity\":5, \"price\":2.0},\n", "              \"eggs\":{\"quantity\":20, \"price\":0.2},\n", "              \"cheese\":{\"quantity\":15, \"price\":3.0},\n", "              \"cookies\":{\"quantity\":8, \"price\":1.0},\n", "              \"juice\":{\"quantity\":12, \"price\":1.2},\n", "              \"yogurt\":{\"quantity\":18, \"price\":1.8},\n", "              \"apples\":{\"quantity\":25, \"price\":2.5},\n", "              \"bananas\":{\"quantity\":30, \"price\":1.3},\n", "              \"oranges\":{\"quantity\":20, \"price\":2.4},\n", "              \"grapes\":{\"quantity\":15, \"price\":3.6}\n", "              }\n", "total_value = 0\n", "for article, number in supermarket.items():\n", "    quantity = number[\"quantity\"]\n", "    peice = number[\"price\"]\n", "    product_price = quantity * peice\n", "    article = article+ ':'\n", "    print(f\"{article:15s} {product_price:0.2f}\")\n", "    total_value += product_price\n", "print(\"-\" * 30)\n", "print(f\"Total value of the supermarket: {total_value:0.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}