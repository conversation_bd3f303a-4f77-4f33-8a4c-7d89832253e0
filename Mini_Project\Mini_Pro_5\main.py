# Sample mock database (replace this with your actual database connection in production)
products_db = [
    {"product_id": 1, "name": "Laptop", "description": "15-inch laptop", "price": 1200, "category": "Electronics", "current_stock": 10},
    {"product_id": 2, "name": "Smartphone", "description": "Android smartphone", "price": 600, "category": "Electronics", "current_stock": 25},
    {"product_id": 3, "name": "T-Shirt", "description": "Cotton t-shirt", "price": 20, "category": "Apparel", "current_stock": 50},
    {"product_id": 5, "name": "<PERSON><PERSON>", "description": "Denim jeans", "price": 40, "category": "Apparel", "current_stock": 30},
    {"product_id": 4, "name": "Running Shoes", "description": "Lightweight running shoes", "price": 80, "category": "Footwear", "current_stock": 15},
]

def view_product_inventory(category_filter=None, min_stock=None): 
    inventory_list = products_db.copy()

    # Apply category filter (case-insensitive)
    if category_filter:
        inventory_list = [p for p in inventory_list if p['category'].lower() == category_filter.lower()]

    # Apply minimum stock filter
    if min_stock is not None:
        inventory_list = [p for p in inventory_list if p['current_stock'] >= min_stock]

    # Format the output for display
    display_data = []
    for product in inventory_list:
        formatted_product = {
            "Product ID": product["product_id"],
            "Name": product["name"],
            "Description": product["description"],
            "Price": product["price"],
            "Current Stock": product["current_stock"]
        }
        display_data.append(formatted_product)
    return display_data

def get_all_categories():
    categories = set()
    for product in products_db:
        categories.add(product['category'])
    return list(categories)

if __name__ == "__main__":
    # Show available categories first
    categories = get_all_categories()
    print("Available Categories:")
    for cat in categories:
        print(f"- {cat}")

    while True:
        user_category = input("\nEnter a category to filter (or leave blank to show all categories): ")
        user_min_stock_input = input("Enter minimum stock to filter (or leave blank for no filter): ")
        if user_min_stock_input.strip() == "":
            user_min_stock = None
        else:
            try:
                user_min_stock = int(user_min_stock_input)
            except ValueError:
                print("Invalid stock input. Defaulting to no stock filter.")
                user_min_stock = None

        filtered_products = view_product_inventory(
            category_filter=user_category if user_category.strip() != "" else  None,
            min_stock=user_min_stock
        )

        # Display the filtered product list
        print("\nFiltered Products:")
        if filtered_products:
            for item in filtered_products:
                print(item)
        else:
            print("No products found matching your filters.")

        # Ask if the user wants to filter again
        again = input("\nWould you like to filter again? (y/n): ")
        if again.lower() != "y":
            break
