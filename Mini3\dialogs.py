# Dialog classes for Inventory Management System

import tkinter as tk
from tkinter import ttk, messagebox
from user_management import user_service
from product_management import product_service
from models import Product, Category, User, Transaction
from config import GUI_CONFIG, USER_ROLES
from typing import Optional, Dict, Any

class BaseDialog:
    """Base class for all dialogs"""
    def __init__(self, parent, title: str, width: int = 400, height: int = 300):
        self.result = None
        self.parent = parent
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{width}x{height}")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.center_dialog()
        
        # Create main frame
        self.main_frame = ttk.Frame(self.dialog, padding=GUI_CONFIG['padding']['medium'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create content
        self.create_content()
        
        # Create buttons
        self.create_buttons()
        
        # Focus on dialog
        self.dialog.focus_set()
    
    def center_dialog(self):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_rootx() + (self.parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = self.parent.winfo_rooty() + (self.parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_content(self):
        """Override in subclasses"""
        pass
    
    def create_buttons(self):
        """Create OK and Cancel buttons"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(GUI_CONFIG['padding']['medium'], 0))
        
        ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT, 
                                                                         padx=(GUI_CONFIG['padding']['small'], 0))
        ttk.Button(button_frame, text="OK", command=self.ok).pack(side=tk.RIGHT)
    
    def ok(self):
        """Handle OK button"""
        if self.validate():
            self.result = self.get_result()
            self.dialog.destroy()
    
    def cancel(self):
        """Handle Cancel button"""
        self.result = None
        self.dialog.destroy()
    
    def validate(self) -> bool:
        """Override in subclasses"""
        return True
    
    def get_result(self) -> Dict[str, Any]:
        """Override in subclasses"""
        return {}

class ProductDialog(BaseDialog):
    """Dialog for adding/editing products"""
    def __init__(self, parent, title: str, product: Optional[Product] = None):
        self.product = product
        self.is_edit = product is not None
        super().__init__(parent, title, 500, 600)
    
    def create_content(self):
        """Create product form content"""
        # Product name
        ttk.Label(self.main_frame, text="Product Name:*").grid(row=0, column=0, sticky='w', 
                                                              pady=GUI_CONFIG['padding']['small'])
        self.name_var = tk.StringVar(value=self.product.name if self.product else "")
        ttk.Entry(self.main_frame, textvariable=self.name_var, width=40).grid(row=0, column=1, 
                                                                             sticky='ew', 
                                                                             padx=(GUI_CONFIG['padding']['small'], 0))
        
        # SKU
        ttk.Label(self.main_frame, text="SKU:*").grid(row=1, column=0, sticky='w', 
                                                     pady=GUI_CONFIG['padding']['small'])
        self.sku_var = tk.StringVar(value=self.product.sku if self.product else "")
        sku_entry = ttk.Entry(self.main_frame, textvariable=self.sku_var, width=40)
        sku_entry.grid(row=1, column=1, sticky='ew', padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Disable SKU editing for existing products
        if self.is_edit:
            sku_entry.config(state='readonly')
        
        # Description
        ttk.Label(self.main_frame, text="Description:").grid(row=2, column=0, sticky='nw', 
                                                            pady=GUI_CONFIG['padding']['small'])
        self.description_var = tk.StringVar(value=self.product.description if self.product else "")
        description_text = tk.Text(self.main_frame, width=40, height=3)
        description_text.grid(row=2, column=1, sticky='ew', padx=(GUI_CONFIG['padding']['small'], 0))
        if self.product and self.product.description:
            description_text.insert('1.0', self.product.description)
        self.description_text = description_text
        
        # Category
        ttk.Label(self.main_frame, text="Category:*").grid(row=3, column=0, sticky='w', 
                                                          pady=GUI_CONFIG['padding']['small'])
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(self.main_frame, textvariable=self.category_var, 
                                          state='readonly', width=37)
        self.category_combo.grid(row=3, column=1, sticky='ew', 
                                padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Load categories
        self.load_categories()
        
        # Price
        ttk.Label(self.main_frame, text="Price:*").grid(row=4, column=0, sticky='w', 
                                                       pady=GUI_CONFIG['padding']['small'])
        self.price_var = tk.StringVar(value=str(self.product.price) if self.product else "")
        ttk.Entry(self.main_frame, textvariable=self.price_var, width=40).grid(row=4, column=1, 
                                                                              sticky='ew', 
                                                                              padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Initial quantity (only for new products)
        if not self.is_edit:
            ttk.Label(self.main_frame, text="Initial Quantity:").grid(row=5, column=0, sticky='w', 
                                                                     pady=GUI_CONFIG['padding']['small'])
            self.quantity_var = tk.StringVar(value="0")
            ttk.Entry(self.main_frame, textvariable=self.quantity_var, width=40).grid(row=5, column=1, 
                                                                                     sticky='ew', 
                                                                                     padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Minimum stock level
        row_offset = 0 if self.is_edit else 1
        ttk.Label(self.main_frame, text="Min Stock Level:").grid(row=5+row_offset, column=0, sticky='w', 
                                                                pady=GUI_CONFIG['padding']['small'])
        self.min_stock_var = tk.StringVar(value=str(self.product.min_stock_level) if self.product else "10")
        ttk.Entry(self.main_frame, textvariable=self.min_stock_var, width=40).grid(row=5+row_offset, column=1, 
                                                                                  sticky='ew', 
                                                                                  padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Supplier
        ttk.Label(self.main_frame, text="Supplier:").grid(row=6+row_offset, column=0, sticky='w', 
                                                         pady=GUI_CONFIG['padding']['small'])
        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(self.main_frame, textvariable=self.supplier_var, 
                                          state='readonly', width=37)
        self.supplier_combo.grid(row=6+row_offset, column=1, sticky='ew', 
                                padx=(GUI_CONFIG['padding']['small'], 0))
        
        # Load suppliers
        self.load_suppliers()
        
        # Required fields note
        ttk.Label(self.main_frame, text="* Required fields", 
                 font=('Arial', 8), foreground='gray').grid(row=7+row_offset, column=0, columnspan=2, 
                                                           sticky='w', pady=GUI_CONFIG['padding']['small'])
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
    
    def load_categories(self):
        """Load categories into combobox"""
        try:
            categories = product_service.get_all_categories()
            category_names = [cat.name for cat in categories]
            self.category_combo['values'] = category_names
            
            # Set current category if editing
            if self.product and self.product.category_id:
                current_category = product_service.get_category_by_id(self.product.category_id)
                if current_category:
                    self.category_var.set(current_category.name)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load categories: {str(e)}")
    
    def load_suppliers(self):
        """Load suppliers into combobox"""
        try:
            suppliers = user_service.get_suppliers()
            supplier_names = [""] + [supplier.full_name for supplier in suppliers]
            self.supplier_combo['values'] = supplier_names
            
            # Set current supplier if editing
            if self.product and self.product.supplier_id:
                current_supplier = user_service.get_current_user()  # This should be improved
                if current_supplier:
                    self.supplier_var.set(current_supplier.full_name)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load suppliers: {str(e)}")
    
    def validate(self) -> bool:
        """Validate form data"""
        if not self.name_var.get().strip():
            messagebox.showerror("Error", "Product name is required")
            return False
        
        if not self.is_edit and not self.sku_var.get().strip():
            messagebox.showerror("Error", "SKU is required")
            return False
        
        if not self.category_var.get():
            messagebox.showerror("Error", "Category is required")
            return False
        
        try:
            price = float(self.price_var.get())
            if price < 0:
                messagebox.showerror("Error", "Price cannot be negative")
                return False
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid price")
            return False
        
        if not self.is_edit:
            try:
                quantity = int(self.quantity_var.get())
                if quantity < 0:
                    messagebox.showerror("Error", "Quantity cannot be negative")
                    return False
            except ValueError:
                messagebox.showerror("Error", "Please enter a valid quantity")
                return False
        
        try:
            min_stock = int(self.min_stock_var.get())
            if min_stock < 0:
                messagebox.showerror("Error", "Minimum stock level cannot be negative")
                return False
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid minimum stock level")
            return False
        
        return True
    
    def get_result(self) -> Dict[str, Any]:
        """Get form data"""
        # Find category ID
        category_id = None
        categories = product_service.get_all_categories()
        for cat in categories:
            if cat.name == self.category_var.get():
                category_id = cat.id
                break
        
        # Find supplier ID
        supplier_id = None
        if self.supplier_var.get():
            suppliers = user_service.get_suppliers()
            for supplier in suppliers:
                if supplier.full_name == self.supplier_var.get():
                    supplier_id = supplier.id
                    break
        
        result = {
            'name': self.name_var.get().strip(),
            'sku': self.sku_var.get().strip(),
            'description': self.description_text.get('1.0', tk.END).strip(),
            'category_id': category_id,
            'price': float(self.price_var.get()),
            'min_stock_level': int(self.min_stock_var.get()),
            'supplier_id': supplier_id
        }
        
        if not self.is_edit:
            result['quantity'] = int(self.quantity_var.get())
        
        return result

class CategoryDialog(BaseDialog):
    """Dialog for adding/editing categories"""
    def __init__(self, parent, title: str, category: Optional[Category] = None):
        self.category = category
        super().__init__(parent, title, 400, 250)

    def create_content(self):
        """Create category form content"""
        # Category name
        ttk.Label(self.main_frame, text="Category Name:*").grid(row=0, column=0, sticky='w',
                                                               pady=GUI_CONFIG['padding']['small'])
        self.name_var = tk.StringVar(value=self.category.name if self.category else "")
        ttk.Entry(self.main_frame, textvariable=self.name_var, width=40).grid(row=0, column=1,
                                                                             sticky='ew',
                                                                             padx=(GUI_CONFIG['padding']['small'], 0))

        # Description
        ttk.Label(self.main_frame, text="Description:").grid(row=1, column=0, sticky='nw',
                                                            pady=GUI_CONFIG['padding']['small'])
        self.description_text = tk.Text(self.main_frame, width=40, height=5)
        self.description_text.grid(row=1, column=1, sticky='ew',
                                  padx=(GUI_CONFIG['padding']['small'], 0))

        if self.category and self.category.description:
            self.description_text.insert('1.0', self.category.description)

        # Required fields note
        ttk.Label(self.main_frame, text="* Required fields",
                 font=('Arial', 8), foreground='gray').grid(row=2, column=0, columnspan=2,
                                                           sticky='w', pady=GUI_CONFIG['padding']['small'])

        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)

    def validate(self) -> bool:
        """Validate form data"""
        if not self.name_var.get().strip():
            messagebox.showerror("Error", "Category name is required")
            return False
        return True

    def get_result(self) -> Dict[str, Any]:
        """Get form data"""
        return {
            'name': self.name_var.get().strip(),
            'description': self.description_text.get('1.0', tk.END).strip()
        }

class ProductDetailsDialog:
    """Dialog for viewing product details"""
    def __init__(self, parent, product: Product):
        self.product = product

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Product Details - {product.name}")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog(parent)

        # Create content
        self.create_content()

    def center_dialog(self, parent):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = parent.winfo_rootx() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_rooty() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def create_content(self):
        """Create product details content"""
        main_frame = ttk.Frame(self.dialog, padding=GUI_CONFIG['padding']['medium'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text=self.product.name,
                 font=GUI_CONFIG['fonts']['title']).pack(pady=(0, GUI_CONFIG['padding']['medium']))

        # Details frame
        details_frame = ttk.LabelFrame(main_frame, text="Product Information",
                                      padding=GUI_CONFIG['padding']['medium'])
        details_frame.pack(fill=tk.BOTH, expand=True)

        # Get additional information
        category = product_service.get_category_by_id(self.product.category_id) if self.product.category_id else None
        supplier = User.get_by_id(self.product.supplier_id) if self.product.supplier_id else None

        # Product details
        details = [
            ("SKU:", self.product.sku),
            ("Category:", category.name if category else "Uncategorized"),
            ("Price:", f"${self.product.price:.2f}"),
            ("Current Stock:", f"{self.product.quantity} units"),
            ("Minimum Stock Level:", f"{self.product.min_stock_level} units"),
            ("Supplier:", supplier.full_name if supplier else "Not assigned"),
            ("Created:", str(self.product.created_at)[:19] if self.product.created_at else "Unknown"),
            ("Last Updated:", str(self.product.updated_at)[:19] if self.product.updated_at else "Unknown")
        ]

        for i, (label, value) in enumerate(details):
            ttk.Label(details_frame, text=label, font=GUI_CONFIG['fonts']['default']).grid(
                row=i, column=0, sticky='w', pady=2, padx=(0, GUI_CONFIG['padding']['small'])
            )
            ttk.Label(details_frame, text=str(value), font=GUI_CONFIG['fonts']['default']).grid(
                row=i, column=1, sticky='w', pady=2
            )

        # Description
        if self.product.description:
            ttk.Label(details_frame, text="Description:", font=GUI_CONFIG['fonts']['default']).grid(
                row=len(details), column=0, sticky='nw', pady=(GUI_CONFIG['padding']['small'], 2),
                padx=(0, GUI_CONFIG['padding']['small'])
            )

            desc_text = tk.Text(details_frame, width=40, height=4, wrap=tk.WORD)
            desc_text.grid(row=len(details), column=1, sticky='ew',
                          pady=(GUI_CONFIG['padding']['small'], 2))
            desc_text.insert('1.0', self.product.description)
            desc_text.config(state='disabled')

        # Stock status
        if self.product.quantity <= 0:
            status = "Out of Stock"
            status_color = GUI_CONFIG['colors']['warning']
        elif self.product.quantity <= self.product.min_stock_level:
            status = "Low Stock"
            status_color = GUI_CONFIG['colors']['secondary']
        else:
            status = "In Stock"
            status_color = GUI_CONFIG['colors']['success']

        status_frame = ttk.Frame(details_frame)
        status_frame.grid(row=len(details)+1, column=0, columnspan=2,
                         pady=GUI_CONFIG['padding']['medium'], sticky='ew')

        ttk.Label(status_frame, text="Status:", font=GUI_CONFIG['fonts']['default']).pack(side=tk.LEFT)
        status_label = tk.Label(status_frame, text=status, font=GUI_CONFIG['fonts']['default'],
                               fg=status_color)
        status_label.pack(side=tk.LEFT, padx=(GUI_CONFIG['padding']['small'], 0))

        # Close button
        ttk.Button(main_frame, text="Close", command=self.dialog.destroy).pack(
            pady=GUI_CONFIG['padding']['medium']
        )

        # Configure grid weights
        details_frame.columnconfigure(1, weight=1)

class TransactionHistoryDialog:
    """Dialog for viewing product transaction history"""
    def __init__(self, parent, product: Product):
        self.product = product

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Transaction History - {product.name}")
        self.dialog.geometry("700x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog(parent)

        # Create content
        self.create_content()

    def center_dialog(self, parent):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = parent.winfo_rootx() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_rooty() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def create_content(self):
        """Create transaction history content"""
        main_frame = ttk.Frame(self.dialog, padding=GUI_CONFIG['padding']['medium'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text=f"Transaction History - {self.product.name}",
                 font=GUI_CONFIG['fonts']['heading']).pack(pady=(0, GUI_CONFIG['padding']['medium']))

        # Transaction list frame
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create treeview for transactions
        columns = ('Date', 'Type', 'Quantity', 'Price', 'User', 'Notes')
        self.transaction_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configure columns
        column_widths = {'Date': 120, 'Type': 80, 'Quantity': 80, 'Price': 80, 'User': 120, 'Notes': 200}

        for col in columns:
            self.transaction_tree.heading(col, text=col)
            self.transaction_tree.column(col, width=column_widths.get(col, 100))

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.transaction_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.transaction_tree.xview)
        self.transaction_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.transaction_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Load transactions
        self.load_transactions()

        # Close button
        ttk.Button(main_frame, text="Close", command=self.dialog.destroy).pack(
            pady=GUI_CONFIG['padding']['medium']
        )

    def load_transactions(self):
        """Load transaction history"""
        try:
            transactions = product_service.get_product_transactions(self.product.id)

            for transaction in transactions:
                # Get user name
                user = User.get_by_id(transaction.user_id)
                user_name = user.full_name if user else "Unknown"

                # Format date
                date_str = str(transaction.created_at)[:19] if transaction.created_at else "Unknown"

                # Format price
                price_str = f"${transaction.price:.2f}" if transaction.price else "N/A"

                self.transaction_tree.insert('', 'end', values=(
                    date_str,
                    transaction.transaction_type,
                    transaction.quantity,
                    price_str,
                    user_name,
                    transaction.notes or ""
                ))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load transaction history: {str(e)}")

class UserDialog(BaseDialog):
    """Dialog for adding/editing users"""
    def __init__(self, parent, title: str, user: Optional[User] = None):
        self.user = user
        self.is_edit = user is not None
        super().__init__(parent, title, 450, 400)

    def create_content(self):
        """Create user form content"""
        # Username
        ttk.Label(self.main_frame, text="Username:*").grid(row=0, column=0, sticky='w',
                                                          pady=GUI_CONFIG['padding']['small'])
        self.username_var = tk.StringVar(value=self.user.username if self.user else "")
        ttk.Entry(self.main_frame, textvariable=self.username_var, width=30).grid(row=0, column=1,
                                                                                 sticky='ew',
                                                                                 padx=(GUI_CONFIG['padding']['small'], 0))

        # Full name
        ttk.Label(self.main_frame, text="Full Name:*").grid(row=1, column=0, sticky='w',
                                                           pady=GUI_CONFIG['padding']['small'])
        self.full_name_var = tk.StringVar(value=self.user.full_name if self.user else "")
        ttk.Entry(self.main_frame, textvariable=self.full_name_var, width=30).grid(row=1, column=1,
                                                                                  sticky='ew',
                                                                                  padx=(GUI_CONFIG['padding']['small'], 0))

        # Email
        ttk.Label(self.main_frame, text="Email:*").grid(row=2, column=0, sticky='w',
                                                       pady=GUI_CONFIG['padding']['small'])
        self.email_var = tk.StringVar(value=self.user.email if self.user else "")
        ttk.Entry(self.main_frame, textvariable=self.email_var, width=30).grid(row=2, column=1,
                                                                              sticky='ew',
                                                                              padx=(GUI_CONFIG['padding']['small'], 0))

        # Role
        ttk.Label(self.main_frame, text="Role:*").grid(row=3, column=0, sticky='w',
                                                      pady=GUI_CONFIG['padding']['small'])
        self.role_var = tk.StringVar()
        role_combo = ttk.Combobox(self.main_frame, textvariable=self.role_var,
                                 state='readonly', width=27)
        role_combo.grid(row=3, column=1, sticky='ew',
                       padx=(GUI_CONFIG['padding']['small'], 0))

        # Set role options
        role_options = [
            ("Administrator", USER_ROLES['ADMIN']),
            ("Inventory Manager", USER_ROLES['INVENTORY_MANAGER']),
            ("Supplier", USER_ROLES['SUPPLIER'])
        ]
        role_combo['values'] = [option[0] for option in role_options]

        # Set current role if editing
        if self.user:
            for display_name, role_value in role_options:
                if self.user.role == role_value:
                    self.role_var.set(display_name)
                    break

        # Password (only for new users)
        if not self.is_edit:
            ttk.Label(self.main_frame, text="Password:*").grid(row=4, column=0, sticky='w',
                                                              pady=GUI_CONFIG['padding']['small'])
            self.password_var = tk.StringVar()
            ttk.Entry(self.main_frame, textvariable=self.password_var, show="*",
                     width=30).grid(row=4, column=1, sticky='ew',
                                   padx=(GUI_CONFIG['padding']['small'], 0))

            ttk.Label(self.main_frame, text="Confirm Password:*").grid(row=5, column=0, sticky='w',
                                                                      pady=GUI_CONFIG['padding']['small'])
            self.confirm_password_var = tk.StringVar()
            ttk.Entry(self.main_frame, textvariable=self.confirm_password_var, show="*",
                     width=30).grid(row=5, column=1, sticky='ew',
                                   padx=(GUI_CONFIG['padding']['small'], 0))

        # Active status (only for editing)
        if self.is_edit:
            ttk.Label(self.main_frame, text="Status:").grid(row=4, column=0, sticky='w',
                                                           pady=GUI_CONFIG['padding']['small'])
            self.is_active_var = tk.BooleanVar(value=self.user.is_active if self.user else True)
            ttk.Checkbutton(self.main_frame, text="Active",
                           variable=self.is_active_var).grid(row=4, column=1, sticky='w',
                                                            padx=(GUI_CONFIG['padding']['small'], 0))

        # Required fields note
        row_offset = 6 if not self.is_edit else 5
        ttk.Label(self.main_frame, text="* Required fields",
                 font=('Arial', 8), foreground='gray').grid(row=row_offset, column=0, columnspan=2,
                                                           sticky='w', pady=GUI_CONFIG['padding']['small'])

        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)

    def validate(self) -> bool:
        """Validate form data"""
        if not self.username_var.get().strip():
            messagebox.showerror("Error", "Username is required")
            return False

        if not self.full_name_var.get().strip():
            messagebox.showerror("Error", "Full name is required")
            return False

        if not self.email_var.get().strip():
            messagebox.showerror("Error", "Email is required")
            return False

        # Basic email validation
        email = self.email_var.get().strip()
        if '@' not in email or '.' not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return False

        if not self.role_var.get():
            messagebox.showerror("Error", "Role is required")
            return False

        # Password validation for new users
        if not self.is_edit:
            password = self.password_var.get()
            confirm_password = self.confirm_password_var.get()

            if not password:
                messagebox.showerror("Error", "Password is required")
                return False

            if len(password) < 6:
                messagebox.showerror("Error", "Password must be at least 6 characters long")
                return False

            if password != confirm_password:
                messagebox.showerror("Error", "Password and confirmation do not match")
                return False

        return True

    def get_result(self) -> Dict[str, Any]:
        """Get form data"""
        # Map display role to actual role value
        role_map = {
            "Administrator": USER_ROLES['ADMIN'],
            "Inventory Manager": USER_ROLES['INVENTORY_MANAGER'],
            "Supplier": USER_ROLES['SUPPLIER']
        }

        result = {
            'username': self.username_var.get().strip(),
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip(),
            'role': role_map.get(self.role_var.get(), USER_ROLES['SUPPLIER'])
        }

        if not self.is_edit:
            result['password'] = self.password_var.get()
        else:
            result['is_active'] = self.is_active_var.get()

        return result

class PasswordResetDialog(BaseDialog):
    """Dialog for resetting user password"""
    def __init__(self, parent, user: User):
        self.user = user
        super().__init__(parent, f"Reset Password - {user.full_name}", 400, 250)

    def create_content(self):
        """Create password reset form content"""
        # User info
        ttk.Label(self.main_frame, text=f"Resetting password for: {self.user.full_name}",
                 font=GUI_CONFIG['fonts']['heading']).grid(row=0, column=0, columnspan=2,
                                                          pady=GUI_CONFIG['padding']['medium'])

        # New password
        ttk.Label(self.main_frame, text="New Password:*").grid(row=1, column=0, sticky='w',
                                                              pady=GUI_CONFIG['padding']['small'])
        self.password_var = tk.StringVar()
        ttk.Entry(self.main_frame, textvariable=self.password_var, show="*",
                 width=30).grid(row=1, column=1, sticky='ew',
                               padx=(GUI_CONFIG['padding']['small'], 0))

        # Confirm password
        ttk.Label(self.main_frame, text="Confirm Password:*").grid(row=2, column=0, sticky='w',
                                                                  pady=GUI_CONFIG['padding']['small'])
        self.confirm_password_var = tk.StringVar()
        ttk.Entry(self.main_frame, textvariable=self.confirm_password_var, show="*",
                 width=30).grid(row=2, column=1, sticky='ew',
                               padx=(GUI_CONFIG['padding']['small'], 0))

        # Generate password button
        ttk.Button(self.main_frame, text="Generate Password",
                  command=self.generate_password).grid(row=3, column=1,
                                                      pady=GUI_CONFIG['padding']['small'])

        # Required fields note
        ttk.Label(self.main_frame, text="* Required fields",
                 font=('Arial', 8), foreground='gray').grid(row=4, column=0, columnspan=2,
                                                           sticky='w', pady=GUI_CONFIG['padding']['small'])

        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)

    def generate_password(self):
        """Generate a random password"""
        import random
        import string

        # Generate a random password
        length = 12
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choice(characters) for _ in range(length))

        self.password_var.set(password)
        self.confirm_password_var.set(password)

        messagebox.showinfo("Generated Password",
                           f"Generated password: {password}\n\nPlease save this password securely.")

    def validate(self) -> bool:
        """Validate form data"""
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()

        if not password:
            messagebox.showerror("Error", "Password is required")
            return False

        if len(password) < 6:
            messagebox.showerror("Error", "Password must be at least 6 characters long")
            return False

        if password != confirm_password:
            messagebox.showerror("Error", "Password and confirmation do not match")
            return False

        return True

    def get_result(self) -> Dict[str, Any]:
        """Get form data"""
        return {
            'password': self.password_var.get()
        }

class UserDetailsDialog:
    """Dialog for viewing user details"""
    def __init__(self, parent, user: User):
        self.user = user

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"User Details - {user.full_name}")
        self.dialog.geometry("450x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.center_dialog(parent)

        # Create content
        self.create_content()

    def center_dialog(self, parent):
        """Center dialog on parent window"""
        self.dialog.update_idletasks()
        x = parent.winfo_rootx() + (parent.winfo_width() // 2) - (self.dialog.winfo_width() // 2)
        y = parent.winfo_rooty() + (parent.winfo_height() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def create_content(self):
        """Create user details content"""
        main_frame = ttk.Frame(self.dialog, padding=GUI_CONFIG['padding']['medium'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text=self.user.full_name,
                 font=GUI_CONFIG['fonts']['title']).pack(pady=(0, GUI_CONFIG['padding']['medium']))

        # Details frame
        details_frame = ttk.LabelFrame(main_frame, text="User Information",
                                      padding=GUI_CONFIG['padding']['medium'])
        details_frame.pack(fill=tk.BOTH, expand=True)

        # User details
        details = [
            ("Username:", self.user.username),
            ("Email:", self.user.email),
            ("Role:", self.user.role.replace('_', ' ').title()),
            ("Status:", "Active" if self.user.is_active else "Inactive"),
            ("Created:", str(self.user.created_at)[:19] if self.user.created_at else "Unknown")
        ]

        for i, (label, value) in enumerate(details):
            ttk.Label(details_frame, text=label, font=GUI_CONFIG['fonts']['default']).grid(
                row=i, column=0, sticky='w', pady=2, padx=(0, GUI_CONFIG['padding']['small'])
            )
            ttk.Label(details_frame, text=str(value), font=GUI_CONFIG['fonts']['default']).grid(
                row=i, column=1, sticky='w', pady=2
            )

        # Status indicator
        status_frame = ttk.Frame(details_frame)
        status_frame.grid(row=len(details), column=0, columnspan=2,
                         pady=GUI_CONFIG['padding']['medium'], sticky='ew')

        if self.user.is_active:
            status_color = GUI_CONFIG['colors']['success']
            status_text = "✓ Active User"
        else:
            status_color = GUI_CONFIG['colors']['warning']
            status_text = "✗ Inactive User"

        status_label = tk.Label(status_frame, text=status_text, font=GUI_CONFIG['fonts']['default'],
                               fg=status_color)
        status_label.pack()

        # Close button
        ttk.Button(main_frame, text="Close", command=self.dialog.destroy).pack(
            pady=GUI_CONFIG['padding']['medium']
        )

        # Configure grid weights
        details_frame.columnconfigure(1, weight=1)

class ForgotPasswordDialog(BaseDialog):
    """Dialog for forgot password functionality"""
    def __init__(self, parent):
        super().__init__(parent, "Forgot Password", 400, 200)

    def create_content(self):
        """Create forgot password form content"""
        # Instructions
        ttk.Label(self.main_frame, text="Enter your email address to reset your password:",
                 font=GUI_CONFIG['fonts']['default']).grid(row=0, column=0, columnspan=2,
                                                          pady=GUI_CONFIG['padding']['medium'])

        # Email field
        ttk.Label(self.main_frame, text="Email:*").grid(row=1, column=0, sticky='w',
                                                       pady=GUI_CONFIG['padding']['small'])
        self.email_var = tk.StringVar()
        ttk.Entry(self.main_frame, textvariable=self.email_var, width=30).grid(row=1, column=1,
                                                                              sticky='ew',
                                                                              padx=(GUI_CONFIG['padding']['small'], 0))

        # Required fields note
        ttk.Label(self.main_frame, text="* Required fields",
                 font=('Arial', 8), foreground='gray').grid(row=2, column=0, columnspan=2,
                                                           sticky='w', pady=GUI_CONFIG['padding']['small'])

        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)

    def validate(self) -> bool:
        """Validate form data"""
        email = self.email_var.get().strip()

        if not email:
            messagebox.showerror("Error", "Email is required")
            return False

        # Basic email validation
        if '@' not in email or '.' not in email:
            messagebox.showerror("Error", "Please enter a valid email address")
            return False

        return True

    def get_result(self) -> Dict[str, Any]:
        """Get form data and process password reset"""
        email = self.email_var.get().strip()

        # Try to reset password
        temp_password = user_service.forgot_password_reset(email)

        if temp_password:
            return {
                'email': email,
                'temp_password': temp_password,
                'success': True
            }
        else:
            messagebox.showerror("Error", "No active user found with that email address")
            return None
