# Main entry point for Inventory Management System

import sys
import os
from database import db
from login_gui import show_login
from main_gui import run_main_application
from user_management import user_service

def initialize_database():
    """Initialize database connection and create tables"""
    print("Initializing database...")
    
    if not db.connect():
        print("Failed to connect to database!")
        return False
    
    if not db.create_tables():
        print("Failed to create database tables!")
        return False
    
    print("Database initialized successfully!")
    return True

def main():
    """Main application entry point"""
    print("=" * 50)
    print("Inventory Management System")
    print("=" * 50)
    
    try:
        # Initialize database
        if not initialize_database():
            input("Press Enter to exit...")
            return
        
        # Show login screen
        print("Starting login interface...")
        login_successful = show_login()
        
        if login_successful:
            current_user = user_service.get_current_user()
            print(f"Login successful! Welcome, {current_user.full_name}")
            
            # Run main application
            print("Starting main application...")
            run_main_application()
            
        else:
            print("Login failed or cancelled.")
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up database connection
        if db.connection:
            db.disconnect()
            print("Database connection closed.")
        
        print("Application terminated.")

if __name__ == "__main__":
    main()
