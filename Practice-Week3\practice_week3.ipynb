{"cells": [{"cell_type": "code", "execution_count": 1, "id": "44cf5d26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON>', '<PERSON>ana', '<PERSON>']\n"]}], "source": ["MyList=['<PERSON>', 'Banana', 'Cherry']\n", "print(MyList)"]}, {"cell_type": "code", "execution_count": 2, "id": "db0bc846", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Banana\n"]}], "source": ["MyList=['<PERSON>', 'Banana', 'Cherry']\n", "print(MyList[1])"]}, {"cell_type": "code", "execution_count": 3, "id": "5fb0f832", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 2, 3, 4, 5]\n"]}], "source": ["number=[1,2,3,4,5,6,7,8,9]\n", "first_five=number[0:5]\n", "\n", "print(first_five)"]}, {"cell_type": "code", "execution_count": 5, "id": "100c6db3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[7, 8, 9]\n"]}], "source": ["number=[1,2,3,4,5,6,7,8,9]\n", "last_three=number[-3:]\n", "\n", "print(last_three)"]}, {"cell_type": "code", "execution_count": 6, "id": "e33e54e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['c', 'd', 'e', 'f', 'g']\n"]}], "source": ["alphabet = ['a','b','c','d','e','f','g','h']\n", "print(alphabet[2:7])\n"]}, {"cell_type": "code", "execution_count": 11, "id": "5b066fa8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2, 4, 6, 8, 10, 12, 14]\n"]}], "source": ["Even_number = [2,3,4,5,6,7,8,9,10,11,12,13,14,15]\n", "all_other=Even_number[::2]\n", "print(all_other)"]}, {"cell_type": "code", "execution_count": 20, "id": "f88bec68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['white', 'blue', 'oranges', 'red']\n"]}], "source": ["colors = ['red', 'oranges','blue','white']\n", "reversed_colors = colors[::-1]\n", "\n", "print(reversed_colors)"]}, {"cell_type": "code", "execution_count": 27, "id": "67ad63be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modified list: ['milk', 'butter', 'cheese', 'juice']\n"]}], "source": ["shopping_list = [\"milk\", \"bread\", \"eggs\", \"juice\"]\n", "\n", "shopping_list[1:3] = [\"butter\", \"cheese\"]\n", "\n", "print(\"Modified list:\", shopping_list)"]}, {"cell_type": "code", "execution_count": 28, "id": "2f044c21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated list: ['milk', 'butter', 'cheese', 'juice', 'yogurt']\n"]}], "source": ["shopping_list.append(\"yogurt\")\n", "\n", "print(\"Updated list:\", shopping_list)"]}, {"cell_type": "code", "execution_count": 29, "id": "2803ad15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extended List: ['milk', 'bread', 'eggs', 'cereal', 'fruit']\n"]}], "source": ["\n", "shopping_list = [\"milk\", \"bread\", \"eggs\"]\n", "new_items = [\"cereal\", \"fruit\"]\n", "shopping_list.extend(new_items)\n", "\n", "print(\"Extended List:\", shopping_list)\n"]}, {"cell_type": "code", "execution_count": 26, "id": "9ad0b6a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modified Tasks List: ['wash dishes', 'clean room', 'pay bills']\n"]}], "source": ["tasks = [\"wash dishes\", \"clean room\", \"do laundry\", \"grocery shopping\", \"pay bills\"]\n", "del tasks[2:4]\n", "\n", "print(\"Modified Tasks List:\", tasks)\n"]}, {"cell_type": "code", "execution_count": null, "id": "b9e7da39", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}