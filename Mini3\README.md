# Inventory Management System

A comprehensive inventory management system built with Python and Tkinter, featuring user management, product tracking, and reporting capabilities.

## Features

### Core Modules
- **User Management**: Multi-role authentication (Admin, Inventory Manager, Supplier)
- **Product Management**: Complete CRUD operations for products and categories
- **Report Management**: Low stock alerts, inventory reports, sales reports, and user activity tracking

### Key Capabilities
- Role-based access control
- Real-time stock tracking
- Low stock alerts and notifications
- Comprehensive reporting with charts
- Transaction history logging
- Multi-database support (SQLite, MySQL, PostgreSQL)

## System Requirements

- Python 3.7 or higher
- Tkinter (usually included with Python)
- Required Python packages (see requirements.txt)

## Installation

1. **Clone or download the project files**

2. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database** (optional):
   - Edit `config.py` to change database settings
   - Default uses SQLite (no additional setup required)
   - For MySQL/PostgreSQL, update connection details in `config.py`

4. **Run the application**:
   ```bash
   python main.py
   ```

## Default Login Credentials

- **Username**: admin
- **Password**: admin123
- **Role**: Administrator

## User Roles and Permissions

### Administrator
- Full system access
- User management (create, update, deactivate users)
- All product and inventory operations
- All reporting capabilities
- System configuration

### Inventory Manager
- Product management (CRUD operations)
- Stock adjustments (in/out)
- Category management
- Generate reports
- View user activity

### Supplier
- View assigned products
- Update product information (limited)
- View reports related to their products

## Database Configuration

The system supports three database types:

### SQLite (Default)
- No additional setup required
- Database file: `inventory.db`
- Ideal for development and small deployments

### MySQL
```python
# In config.py
CURRENT_DB = 'mysql'
DATABASE_CONFIG['mysql'] = {
    'host': 'localhost',
    'port': 3306,
    'database': 'inventory_db',
    'user': 'your_username',
    'password': 'your_password'
}
```

### PostgreSQL
```python
# In config.py
CURRENT_DB = 'postgresql'
DATABASE_CONFIG['postgresql'] = {
    'host': 'localhost',
    'port': 5432,
    'database': 'inventory_db',
    'user': 'your_username',
    'password': 'your_password'
}
```

## File Structure

```
inventory-management-system/
├── main.py                 # Application entry point
├── config.py              # Configuration settings
├── database.py            # Database connection and management
├── models.py              # Data models
├── user_management.py     # User management service
├── product_management.py  # Product management service
├── report_management.py   # Report management service
├── login_gui.py          # Login interface
├── main_gui.py           # Main application interface
├── dashboard_gui.py      # Dashboard with analytics
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## Usage Guide

### First Time Setup
1. Run the application: `python main.py`
2. Login with default admin credentials
3. Create additional users as needed
4. Set up product categories
5. Add products to inventory

### Daily Operations
1. **Stock Management**:
   - Add new products
   - Adjust stock levels
   - Process incoming/outgoing inventory

2. **Monitoring**:
   - Check dashboard for overview
   - Review low stock alerts
   - Monitor recent activity

3. **Reporting**:
   - Generate inventory reports
   - Review sales performance
   - Track user activity

### Administrative Tasks
1. **User Management**:
   - Create new user accounts
   - Assign appropriate roles
   - Deactivate inactive users

2. **System Maintenance**:
   - Regular database backups
   - Review system logs
   - Update configuration as needed

## Features in Detail

### Dashboard
- Real-time statistics overview
- Stock status distribution charts
- Category analysis
- Recent transaction activity
- Low stock alerts

### Product Management
- Add/edit/delete products
- Category organization
- SKU management
- Price tracking
- Supplier assignment
- Stock level monitoring

### Reporting System
- **Low Stock Report**: Products below minimum levels
- **Inventory Report**: Complete stock overview
- **Sales Report**: Transaction analysis with charts
- **User Activity Report**: Track user actions

### Security Features
- Password hashing (SHA-256)
- Role-based access control
- Session management
- User activity logging

## Troubleshooting

### Common Issues

1. **Database Connection Error**:
   - Check database configuration in `config.py`
   - Ensure database server is running (MySQL/PostgreSQL)
   - Verify credentials and permissions

2. **Import Errors**:
   - Install required packages: `pip install -r requirements.txt`
   - Check Python version compatibility

3. **GUI Display Issues**:
   - Ensure Tkinter is installed
   - Check display settings and resolution
   - Try different themes in configuration

### Error Logs
- Check console output for detailed error messages
- Database errors are logged with specific details
- GUI errors include stack traces for debugging

## Development and Extension

### Adding New Features
1. Create new service modules following existing patterns
2. Add GUI components in separate files
3. Update main_gui.py to integrate new features
4. Add appropriate permissions and role checks

### Database Schema Extensions
1. Add new tables in database.py `create_tables()` method
2. Create corresponding model classes in models.py
3. Update service classes with new functionality

### Customization
- Modify `config.py` for appearance and behavior
- Update GUI_CONFIG for colors, fonts, and layout
- Adjust stock thresholds and alert levels

## Support and Maintenance

### Regular Maintenance
- Backup database regularly
- Monitor disk space for SQLite databases
- Review and archive old transaction logs
- Update user permissions as needed

### Performance Optimization
- Index frequently queried database columns
- Implement data archiving for old records
- Optimize chart rendering for large datasets
- Consider database connection pooling for high usage

## License

This project is provided as-is for educational and commercial use. Modify and distribute according to your needs.

## Version History

- **v1.0.0**: Initial release with core functionality
  - User management system
  - Product and category management
  - Basic reporting capabilities
  - Dashboard with analytics
  - Multi-database support
