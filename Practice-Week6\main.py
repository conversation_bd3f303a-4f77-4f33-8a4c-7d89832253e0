# ...existing code until Quiz Game section...
#quiz_7 
def palindrome_check():
    while True:
        # Display menu
        print("\nPalindrome Checker Menu")
        print("1. Enter Text to Check")
        print("2. Exit")
        
        # Get user choice
        choice = input("Enter your choice (1 or 2): ").strip()
        
        if choice == '1':
            # Get input and process
            text = input("\nEnter a word or phrase: ").strip()
            
            # Convert to lowercase and remove spaces
            clean_text = text.lower().replace(" ", "")
            
            # Convert to list of characters
            char_list = list(clean_text)
            
            # Create reversed list using slicing
            reversed_list = char_list[::-1]
            
            # Check palindrome status
            if char_list == reversed_list:
                print(f'"{text}" is a palindrome!')
            else:
                print(f'"{text}" is NOT a palindrome.')
        
        elif choice == '2':
            print("Exiting program. Goodbye!")
            break
        
        else:
            print("Invalid choice. Please enter 1 or 2.")
# Quiz Game

questions = [
    ["What is 2+2?", ["3", "4", "5"], 1, "Basic math question"],
    ["What color is the sky?", ["Blue", "Green", "Red"], 0, "Nature question"],
    ["Which planet is closest to the Sun?", ["Earth", "Venus", "Mercury"], 2, "Astronomy question"],
    ["What is the capital of Cambodia?", ["Hannuy", "Phnom Penh", "Berlin"], 1, "Geography question"],
    ["How many continents are there?", ["5", "6", "7"], 2, "Geography question"]
]

def start_quiz():
    score = 0
    total_questions = len(questions)
    remaining_questions = questions.copy()
    
    print("\n=== Quiz Started ===")
    print(f"Total questions: {total_questions}")
    
    while remaining_questions:
        # Get a question and remove it from remaining questions
        question = remaining_questions.pop(0)
        print(f"\nCategory: {question[3]}")
        print(f"Question: {question[0]}")
        
        # Display options
        for i, option in enumerate(question[1], 1):
            print(f"{i}. {option}")
        
        # Get and validate user answer
        try:
            answer = int(input("\nYour answer (1-3): ")) - 1
            if answer < 0 or answer > 2:
                print("Invalid option! Answer must be between 1 and 3.")
                continue
                
            if answer == question[2]:
                score += 1
                print("Correct! ✓")
            else:
                correct_answer = question[1][question[2]]
                print(f"Wrong! ✗ The correct answer was: {correct_answer}")
                
        except ValueError:
            print("Invalid input! Please enter a number.")
            continue
            
        print(f"Current score: {score}/{total_questions-len(remaining_questions)}")
    
    # Final score and performance evaluation
    percentage = (score / total_questions) * 100
    print("\n=== Quiz Complete ===")
    print(f"Final score: {score}/{total_questions}")
    print(f"Percentage: {percentage:.1f}%")
    
    if percentage == 100:
        print("Perfect score! Excellent work! 🏆")
    elif percentage >= 80:
        print("Great job! 👏")
    elif percentage >= 60:
        print("Good effort! 👍")
    else:
        print("Keep practicing! 📚")


def view_questions():
    """
    Display all quiz questions with detailed information for review and debugging.
    Shows question number, category, question text, all options, and correct answer.
    """
    print("\n=== Quiz Question Review ===")
    print(f"Total questions in database: {len(questions)}\n")
    
    for i, q in enumerate(questions, 1):
        print(f"Question #{i}")
        print(f"{'='*40}")
        print(f"Category: {q[3]}")
        print(f"Question text: {q[0]}")
        print("\nOptions:")
        for j, option in enumerate(q[1]):
            marker = "✓" if j == q[2] else " "
            print(f"  {j+1}. {option} {marker}")
        print(f"Correct answer: {q[1][q[2]]}")
        print(f"Answer index: {q[2]}")
        print(f"{'-'*40}\n")


def quiz_game():
    print("Welcome to the Quiz Game!")
    while True:
        print("\nMenu:")
        print("1. Start Quiz")
        print("2. View Questions (Debug/Review Mode)")
        print("3. Exit")
        
        choice = input("Enter your choice (1-3): ")
        
        if choice == '1':
            start_quiz()
        elif choice == '2':
            view_questions()
        elif choice == '3':
            print("Exiting the game. Goodbye!")
            break
        else:
            print("Invalid choice! Please select a valid option.")
def main():
    print("Welcome to the Quiz and Palindrome Checker Program!")
    while True:  # Add continuous loop
        print("\nChoose an option:")
        print("1. Palindrome Check")
        print("2. Quiz Game")
        print("3. Exit")  # Add exit option
        
        choice = input("Enter your choice (1-3): ").strip()
        
        if choice == '1':
            palindrome_check()
        elif choice == '2':
            quiz_game()
        elif choice == '3':  # Add exit condition
            print("Thank you for using the program. Goodbye!")
            break
        else:
            print("Invalid choice! Please enter 1, 2, or 3.")
if __name__ == "__main__":
    main()
